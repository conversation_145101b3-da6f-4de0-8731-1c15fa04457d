#!/bin/bash
pre_version=$1
dataware_version=$2

pwd=`pwd`
source ../../../properties/bigdata-common.properties

echo `date '+%Y-%m-%d %H:%M:%S'`'开始升级next-calculate'

echo `date '+%Y-%m-%d %H:%M:%S'`'开始分发dataset-etl到'$gdp_server_deploy
sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'mkdir -p ~/deploy/onedata/dataware/native'
sshpass -p $glory_deploy_password scp -o StrictHostKeyChecking=no libnative-$dataware_version.so $gdp_server_deploy:~/deploy/onedata/dataware/native
echo `date '+%Y-%m-%d %H:%M:%S'`'结束分发dataset-etl到'$gdp_server_deploy

echo `date '+%Y-%m-%d %H:%M:%S'`'结束升级dataset-etl'