use crate::config::DwTestItemConfig;
use crate::service::dim::test_item_service::TestItemService;
use crate::service::dim::test_program_test_item_service::TestProgramTestItemService;
use crate::service::dim::{
    get_test_program_partition, write_to_ck_by_partition, write_to_clickhouse_after_tombstone, write_to_hdfs,
};
use bumpalo::Bump;
use common::dim::sink::{TestItemHandler, TestProgramTestItemHandler};
use common::dto::dim::{DimTestItem, DimTestItemRow, DimTestProgramTestItem, DimTestProgramTestItemRow};
use common::dto::dwd::file_detail::FileDetail;
use common::dto::dwd::sub_test_item_detail::SubTestItemDetail;
use common::model::constant::dw_layer::DwLayer;
use common::model::dw_table_calculate_step::DwTableCalculateStep;
use common::model::dw_table_enum::DwTableEnum;
use common::model::key::WaferKey;
use common::model::table_calculate_info::TableCalculateInfo;
use common::service::dw_table_calculate_record_service::DwTableCalculateRecordService;
use common::utils::path;
use mysql_provider::MySqlProviderImpl;
use std::collections::HashMap;
use std::error::Error;

/// CpDimTestItemService handles CP (Contact Probe) stage DIM layer processing
/// Corresponds to CpDimTestItemService.scala
#[derive(Debug, Clone)]
pub struct CpDimTestItemService {
    properties: DwTestItemConfig,
}

impl CpDimTestItemService {
    pub fn new(properties: DwTestItemConfig) -> Self {
        Self { properties }
    }

    /// Calculate CP DIM layer data
    /// Corresponds to calculate method in CpDimTestItemService.scala
    pub async fn calculate(
        &self,
        sub_test_item: &Vec<Vec<SubTestItemDetail>>,
        file_detail_map: &HashMap<i64, FileDetail>,
        wafer_key: WaferKey,
        test_area: &str,
        execute_mode: &str,
        file_category: &str,
        run_mode: &str,
        mysql_provider: &MySqlProviderImpl,
    ) -> Result<(), Box<dyn Error + Send + Sync>> {
        log::info!("开始计算 CP DIM 层数据...");
        let bump = Bump::new();
        let dim_db_name = &self.properties.dim_db_name;
        let mysql_config = self.properties.get_mysql_config();
        let mut calculate_time_map: HashMap<DwTableEnum, TableCalculateInfo> = HashMap::new();
        let table_calculate_record_service = DwTableCalculateRecordService::new(
            common::model::constant::upload_type::UploadType::AUTO,
            None,
            String::new(),
        );

        // 1. 计算 DIM TestItem
        log::info!("开始计算 DIM TestItem...");
        table_calculate_record_service.update_dw_table_calculate(
            &mut calculate_time_map,
            DwTableEnum::DimTestItem,
            DwTableCalculateStep::CalculateStart,
            Some(self.properties.dim_db_name.clone()),
        );
        let test_item_service = TestItemService::new(self.properties.clone());
        let test_items = bump.alloc(
            test_item_service
                .calculate_test_item(sub_test_item, file_detail_map, true)
                .await?,
        );
        log::info!("DIM TestItem 计算完成，共生成 {} 条记录", test_items.len());

        table_calculate_record_service.update_dw_table_calculate(
            &mut calculate_time_map,
            DwTableEnum::DimTestItem,
            DwTableCalculateStep::CalculateEnd,
            Some(self.properties.dim_db_name.clone()),
        );

        // Write DimTestItem to HDFS (Parquet)
        CpDimTestItemService::write_test_items_to_hdfs(&self.properties, &test_items, &wafer_key).await?;

        table_calculate_record_service.update_dw_table_calculate(
            &mut calculate_time_map,
            DwTableEnum::DimTestItem,
            DwTableCalculateStep::SinkCkStart,
            Some(self.properties.dim_db_name.clone()),
        );

        // Convert DimTestItem to DimTestItemRow for ClickHouse storage
        let test_item_rows = bump.alloc(
            test_items
                .iter()
                .map(|item| DimTestItemRow::from_hdfs_entity(item))
                .collect::<Vec<_>>(),
        );

        // Write DimTestItem to ClickHouse
        let test_item_handler = TestItemHandler::new(dim_db_name.to_string(), self.properties.insert_cluster_table);
        write_to_clickhouse_after_tombstone(test_item_handler, &self.properties, &test_item_rows, &wafer_key).await?;

        table_calculate_record_service.update_dw_table_calculate(
            &mut calculate_time_map,
            DwTableEnum::DimTestItem,
            DwTableCalculateStep::SinkCkEnd,
            Some(self.properties.dim_db_name.clone()),
        );

        // 2. 计算 DIM TestProgramTestItem
        log::info!("开始计算 DIM TestProgramTestItem...");
        table_calculate_record_service.update_dw_table_calculate(
            &mut calculate_time_map,
            DwTableEnum::DimTestProgramTestItem,
            DwTableCalculateStep::CalculateStart,
            Some(self.properties.dim_db_name.clone()),
        );

        let test_program_test_item_service = TestProgramTestItemService::new(self.properties.clone());
        let test_program_test_item = bump.alloc(
            test_program_test_item_service
                .calculate_test_program_test_item(&test_items)
                .await?,
        );
        log::info!("DIM TestProgramTestItem 计算完成");

        table_calculate_record_service.update_dw_table_calculate(
            &mut calculate_time_map,
            DwTableEnum::DimTestProgramTestItem,
            DwTableCalculateStep::CalculateEnd,
            Some(self.properties.dim_db_name.clone()),
        );

        // Write DimTestProgramTestItem to HDFS (Parquet)
        CpDimTestItemService::write_test_program_test_items_to_hdfs(
            &self.properties,
            &test_program_test_item,
            &wafer_key,
        )
        .await?;

        table_calculate_record_service.update_dw_table_calculate(
            &mut calculate_time_map,
            DwTableEnum::DimTestProgramTestItem,
            DwTableCalculateStep::SinkCkStart,
            Some(self.properties.dim_db_name.clone()),
        );

        // Convert DimTestProgramTestItem to DimTestProgramTestItemRow for ClickHouse storage
        let test_program_test_item_rows = bump.alloc(
            test_program_test_item
                .iter()
                .map(|item| DimTestProgramTestItemRow::from_hdfs_entity(item))
                .collect::<Vec<_>>(),
        );

        // Write DimTestProgramTestItem to ClickHouse
        let test_program_test_item_handler = TestProgramTestItemHandler::new(dim_db_name.to_string());
        let partition = get_test_program_partition(
            wafer_key.customer.as_str(),
            wafer_key.test_area.as_str(),
            wafer_key.factory.as_str(),
        );
        write_to_ck_by_partition(
            &self.properties,
            &test_program_test_item_rows,
            test_program_test_item_handler,
            partition.as_str(),
        )
        .await?;

        table_calculate_record_service.update_dw_table_calculate(
            &mut calculate_time_map,
            DwTableEnum::DimTestProgramTestItem,
            DwTableCalculateStep::SinkCkEnd,
            Some(self.properties.dim_db_name.clone()),
        );

        log::info!("DIM TestProgramTestItem 写入clickhouse完成");

        // 保存表计算记录(耗时、资源)
        table_calculate_record_service
            .save_dw_table_calculate(
                &wafer_key.customer,
                &wafer_key.sub_customer,
                &wafer_key.factory,
                &wafer_key.factory_site,
                test_area,
                &wafer_key.device_id,
                &wafer_key.lot_id,
                &wafer_key.wafer_no,
                file_category,
                &wafer_key.lot_type,
                &wafer_key.test_stage,
                execute_mode,
                mysql_config,
                &calculate_time_map,
                DwLayer::DIM,
                run_mode,
                Some(common::model::constant::upload_type::UploadType::AUTO),
                mysql_provider,
            )
            .await?;

        drop(bump);
        log::info!("CP DIM 层数据计算完成");
        Ok(())
    }

    /// Write DimTestItem data to HDFS using Parquet format
    async fn write_test_items_to_hdfs(
        config: &DwTestItemConfig,
        data: &[DimTestItem],
        wafer_key: &WaferKey,
    ) -> Result<(), Box<dyn Error + Send + Sync>> {
        if data.is_empty() {
            log::warn!("No DimTestItem data to write to HDFS");
            return Ok(());
        }

        let table_path = path::get_wafer_path(
            &config.cp_dim_result_dir_template,
            DwTableEnum::DimTestItem.get_dir_table_name().as_str(),
            &wafer_key.test_area,
            &wafer_key.customer,
            &wafer_key.factory,
            &wafer_key.lot_id,
            &wafer_key.wafer_no,
            &wafer_key.device_id,
            &wafer_key.test_stage,
            &wafer_key.lot_type,
        );

        log::info!("写入DimTestItem parquet文件到路径: {}", table_path);

        write_to_hdfs(table_path.as_str(), data, config.get_batch_size()?, &config.get_hdfs_config()).await?;
        log::info!("成功写入DimTestItem parquet文件到路径: {}", table_path);

        Ok(())
    }

    /// Write DimTestProgramTestItem data to HDFS using Parquet format
    async fn write_test_program_test_items_to_hdfs(
        config: &DwTestItemConfig,
        data: &[DimTestProgramTestItem],
        wafer_key: &WaferKey,
    ) -> Result<(), Box<dyn Error + Send + Sync>> {
        if data.is_empty() {
            log::warn!("No DimTestProgramTestItem data to write to HDFS");
            return Ok(());
        }

        let table_path = path::get_wafer_path(
            &config.cp_dim_result_dir_template,
            DwTableEnum::DimTestProgramTestItem.get_dir_table_name().as_str(),
            &wafer_key.test_area,
            &wafer_key.customer,
            &wafer_key.factory,
            &wafer_key.lot_id,
            &wafer_key.wafer_no,
            &wafer_key.device_id,
            &wafer_key.test_stage,
            &wafer_key.lot_type,
        );

        log::info!("写入DimTestProgramTestItem parquet文件到路径: {}", table_path);
        write_to_hdfs(table_path.as_str(), data, config.get_batch_size()?, &config.get_hdfs_config()).await?;
        log::info!("成功写入DimTestProgramTestItem parquet文件到路径: {}", table_path);

        Ok(())
    }
}
