use crate::config::DwTestItemConfig;
use ck_provider::CkProviderError::ExecutionError;
use common::ck::ck_operate::CkOperate;
use common::ck::ck_sink::{CkSink, SinkHandler};
use common::dto::dim::{DimTestItem, DimTestProgramTestItem};
use common::model::constant::{CUSTOMER, DIM_TEST_PROGRAM_PARTITION_TEMPLATE, FACTORY, TEST_AREA};
use common::model::dw_table_enum::DwTableEnum;
use common::model::key::Wafer<PERSON>ey;
use common::parquet::RecordBatchWrapper;
use common::utils::path;
use parquet_provider::hdfs_provider::HdfsConfig;
use parquet_provider::parquet_provider::write_parquet_multi;
use std::error::Error;

pub mod cp_dim_test_item_service;
pub mod ft_dim_test_item_service;
pub mod test_item_service;
pub mod test_program_test_item_service;

async fn write_to_ck_by_partition<T>(
    config: &DwTestItemConfig,
    data: &[T],
    handler: impl SinkHandler + Send + Sync,
    partition: &str,
) -> Result<(), Box<dyn Error + Send + Sync>>
where
    T: clickhouse::Row + serde::Serialize + Send + Sync + Clone + 'static,
{
    use common::ck::ck_sink::CkSink;

    if data.is_empty() {
        log::info!("No data to write.");
        return Ok(());
    }

    log::info!("Writing {} records to ClickHouse.", data.len());

    let ck_config = config.get_ck_config(handler.db_name());

    CkSink::write_to_ck_with_partition_from_config(data, &config.dim_num_partitions, ck_config, &handler, partition)
        .await
        .map_err(|e| -> Box<dyn Error + Send + Sync> {
            log::error!("写入clickhouse 失败: {}, 数据量为: {}", handler.table_name(), &data.len());
            Box::new(std::io::Error::new(std::io::ErrorKind::Other, e.to_string()))
        })?;

    log::info!("Successfully wrote {} records to ClickHouse.", data.len());
    Ok(())
}

pub async fn write_to_clickhouse_after_tombstone<T>(
    handler: impl SinkHandler + Send + Sync,
    config: &DwTestItemConfig,
    data: &[T],
    wafer_key: &WaferKey,
) -> color_eyre::Result<()>
where
    T: clickhouse::Row + serde::Serialize + Send + Sync + Clone + 'static,
{
    tombstone(&handler, config, &wafer_key, None).await?;
    CkSink::write_to_ck(
        data,
        config.dim_num_partitions.parse::<usize>()?,
        &config.get_ck_config(handler.db_name()),
        &handler,
        false,
    )
    .await
    .map_err(|e| {
        log::error!("写入clickhouse失败: {}", e);
        let err_str = e.to_string();
        ExecutionError(err_str)
    })?;
    Ok(())
}

pub async fn tombstone(
    handler: &(impl SinkHandler + Send + Sync),
    config: &DwTestItemConfig,
    wafer_key: &WaferKey,
    lot_bucket: Option<i32>,
) -> color_eyre::Result<()> {
    let table_full_name = format!("{}.{}", handler.db_name(), handler.table_name());
    CkOperate::tombstone_ck(
        &wafer_key.customer,
        &wafer_key.factory,
        &wafer_key.test_area,
        &wafer_key.lot_id,
        &wafer_key.lot_type,
        &wafer_key.test_stage,
        &wafer_key.device_id,
        lot_bucket,
        &wafer_key.wafer_no,
        &table_full_name,
        &config.pick_random_ck_node_host(),
        &config.get_ck_address_list(),
        &config.ck_username,
        &config.ck_password,
        handler.partition_expr(),
        Some(chrono::Utc::now()),
    )
    .await
    .map_err(|e| {
        let err_str = e.to_string();
        log::error!("执行tombstone操作失败: {}", e);
        crate::error::DatawareError::TombstoneFailed(err_str)
    })?;

    Ok(())
}

pub fn get_test_program_partition(customer: &str, test_area: &str, factory: &str) -> String {
    DIM_TEST_PROGRAM_PARTITION_TEMPLATE
        .replace(CUSTOMER, customer)
        .replace(TEST_AREA, test_area)
        .replace(FACTORY, factory)
}

/// Write DimTestProgramTestItem data to HDFS using Parquet format
async fn write_to_hdfs<T>(table_path: &str, data: &[T], batch_size: usize,hdfs_config: &HdfsConfig) -> Result<(), Box<dyn Error + Send + Sync>>
where
    T: RecordBatchWrapper + Send + Sync + Clone + 'static,
{
    if data.is_empty() {
        log::warn!("No data to write to HDFS");
        return Ok(());
    }

    log::info!("写入parquet文件到路径: {}", table_path);

    // 将数据包装成Vec<Vec<T>>格式以匹配write_parquet_multi的期望
    let data_vec = data.to_vec();
    let data_batches = vec![data_vec];
    let data_refs = data_batches.iter().collect();
    write_parquet_multi(
        &table_path,
        &data_refs,
        Some(hdfs_config),
        batch_size, // batch_size
    )
    .await
    .map_err(|e| {
        Box::new(std::io::Error::new(std::io::ErrorKind::Other, format!("写入parquet文件失败: {}", e)))
            as Box<dyn Error + Send + Sync>
    })?;
    log::info!("成功写入parquet文件到路径: {}", table_path);

    Ok(())
}
