use std::collections::HashMap;
use chrono::{DateTime, Utc};
use serde_json;
use log::info;
use std::sync::Arc;

use common::dws::dws_service::{DwsSubDieDetail, DwsSubTestItemDetail};
use common::dto::dws::test_program_test_order::TestProgramTestOrder;
use common::model::constant::{COMMA, EMPTY, SYSTEM};
use common::model::key::lot_key::LotKey;
use common::repository::mysql::test_program_test_plan_repository::TestProgramTestPlanRepository;
use common::repository::ck::test_program_test_order_repository::TestProgramTestOrderRepository;
use mysql_provider::{MySqlConfig, MySqlProviderImpl};
use ck_provider::CkConfig;

use crate::service::test_program_test_order_common_service::TestProgramTestOrderCommonService;

#[derive(Debug, Clone)]
pub struct TestProgramTestOrderService {
    pub test_area: String,
    pub common_service: TestProgramTestOrderCommonService,
}

impl TestProgramTestOrderService {
    pub fn new(test_area: String) -> Self {
        Self {
            test_area,
            common_service: TestProgramTestOrderCommonService::new(),
        }
    }

    /// Main calculation method equivalent to calculate() in Scala
    /// Processes die details and test item details to generate test program test orders
    pub async fn calculate(
        &self,
        die_detail: &Vec<Arc<DwsSubDieDetail>>,
        test_item_detail: &Vec<Arc<DwsSubTestItemDetail>>,
        mysql_config: MySqlConfig,
        ck_config: CkConfig,
        lot_key: &LotKey,
        upload_type: &str,
        redis_provider: &redis_provider::provider::RedisProvider,
        write_parquet_path: &str,
        dim_db_name: &str,
        dim_result_partition: i32,
        index_num_partition: &str,
        mysql_provider: &MySqlProviderImpl
    ) -> Result<(), Box<dyn std::error::Error>> {
        // Get distinct test programs from die details
        let cal_test_program_test_order_programs: Vec<String> = die_detail
            .iter()
            .map(|d| d.TEST_PROGRAM.to_string())
            .collect::<std::collections::HashSet<_>>()
            .into_iter()
            .collect();

        // Check which test programs can be initialized
        let test_program_test_order_repository = TestProgramTestOrderRepository::new(
            dim_db_name.to_string(),
            ck_config.clone(),
        );
        let cal_test_program_test_order_programs = test_program_test_order_repository
            .can_init_test_program_test_order(lot_key, upload_type, &cal_test_program_test_order_programs)
            .await?;

        // Calculate initial test order
        let program_with_test_item_map = self.cal_init_test_order(
            die_detail,
            test_item_detail,
            &cal_test_program_test_order_programs,
        )?;

        // Write test program test order data
        self.common_service
            .write_test_program_test_order(
                &program_with_test_item_map,
                dim_db_name,
                mysql_config,
                ck_config,
                redis_provider,
                lot_key,
                upload_type,
                mysql_provider
            )
            .await?;

        Ok(())
    }

    /// Calculate initial test order equivalent to calInitTestOrder in Scala
    fn cal_init_test_order(
        &self,
        die_detail: &Vec<Arc<DwsSubDieDetail>>,
        test_item_detail: &Vec<Arc<DwsSubTestItemDetail>>,
        cal_test_program_test_order_programs: &[String],
    ) -> Result<HashMap<String, HashMap<String, TestProgramTestOrder>>, Box<dyn std::error::Error>> {
        // Filter pass bin dies and group by FILE_ID, then find first die for each file
        let mut file_first_dies = Vec::new();
        let mut grouped_by_file: HashMap<i64, Vec<&Arc<DwsSubDieDetail>>> = HashMap::new();
        
        for die in die_detail {
            if self.common_service.filter_pass_bin(die.as_ref(), cal_test_program_test_order_programs) {
                if let Some(file_id) = die.FILE_ID {
                    grouped_by_file.entry(file_id).or_insert_with(Vec::new).push(die);
                }
            }
        }

        // Find first die for each file
        for (_, dies) in grouped_by_file {
            let dies_refs: Vec<&DwsSubDieDetail> = dies.iter().map(|arc| arc.as_ref()).collect();
            if let Some(first_die) = self.common_service.find_file_first_die(&dies_refs) {
                file_first_dies.push(first_die);
            }
        }

        // Find first die for each program
        let program_first_die = self.common_service.find_program_first_die(&file_first_dies);

        // Filter test item details and generate test program test orders
        let mut test_program_test_orders = HashMap::new();
        for test_item in test_item_detail {
            let program = &test_item.TEST_PROGRAM;
            let first_die = program_first_die.get(program.as_ref());
            
            if self.common_service.predict_program_first_die_test(test_item, first_die) {
                let test_order = self.common_service.generate_test_program_test_order(test_item);
                test_program_test_orders.entry(program.clone()).or_insert_with(Vec::new).push(test_order);
            }
        }

        // Calculate test order
        let all_orders: Vec<TestProgramTestOrder> = test_program_test_orders
            .into_values()
            .flatten()
            .collect();
        let result = self.common_service.cal_test_order(&all_orders);
        Ok(result)
    }
}