use log::{debug, error, info};
use opendal::{services, Operator, EntryMode};
use serde::{Deserialize, Serialize};
use std::fs;
use std::io::Write;
use std::path::Path;
use std::{env, io};
use thiserror::Error;
use walkdir::WalkDir;


#[derive(Error, Debug)]
pub enum HdfsError {
    #[error("OpenDAL error: {0}")]
    OpenDALError(#[from] opendal::Error),
    #[error("IO error occurred while communicating with HDFS: {0}")]
    IOError(#[from] io::Error),
    #[error("data transfer error: {0}")]
    DataTransferError(String),
    #[error("checksums didn't match")]
    ChecksumError,
    #[error("invalid path: {0}")]
    InvalidPath(String),
    #[error("invalid argument: {0}")]
    InvalidArgument(String),
    #[error("file already exists: {0}")]
    AlreadyExists(String),
    #[error("operation failed: {0}")]
    OperationFailed(String),
    #[error("file not found: {0}")]
    FileNotFound(String),
    #[error("blocks not found for {0}")]
    BlocksNotFound(String),
    #[error("path is a directory: {0}")]
    IsADirectoryError(String),
    #[error("unsupported erasure coding policy {0}")]
    UnsupportedErasureCodingPolicy(String),
    #[error("erasure coding error: {0}")]
    ErasureCodingError(String),
    #[error("operation not supported: {0}")]
    UnsupportedFeature(String),
    #[error("interal error, this shouldn't happen: {0}")]
    InternalError(String),
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HdfsConfig {
    pub url: String,
    pub user: String,
    pub hadoop_home: String,
}

impl HdfsConfig {
    pub fn new(url:&str) -> Self {
        // 尝试从环境变量获取HADOOP_HOME，如果没有则使用默认路径
        let hadoop_home = std::env::var("HADOOP_HOME")
            .unwrap_or_else(|_| "/Users/<USER>/Downloads/hadoop".to_string());
            
        Self {
            url: url.to_string(),
            user: "glory".to_string(),
            hadoop_home,
        }
    }
}

pub struct HdfsProvider {
    pub client: Operator,
    pub config: HdfsConfig,
}

impl HdfsProvider {
    pub fn new(config: HdfsConfig) -> Result<Self, HdfsError> {
        info!("Initializing HDFS provider with HADOOP_HOME: {}", config.hadoop_home);
        let mut service_config = services::HdfsConfig::default();
        unsafe {
            env::set_var("CLASSPATH", HdfsProvider::generate_hadoop_classpath(Path::new(&config.hadoop_home)));
        }
        unsafe {
            env::set_var("HADOOP_CONF_DIR", format!("{}/conf", &config.hadoop_home));
        }
        unsafe {
            env::set_var("HADOOP_HOME", &config.hadoop_home);
        }
        service_config.name_node = Some(config.url.clone());
        let op: Operator = Operator::from_config(service_config)?.finish();

        Ok(Self { client: op, config })
    }

    fn generate_hadoop_classpath(hadoop_home: &Path) -> String {
        // 1. 递归地遍历目录，找到所有 .jar 文件。
        let jar_files: Vec<String> = WalkDir::new(hadoop_home)
            .into_iter()
            // filter_map 会过滤掉遍历过程中产生的错误（比如权限问题）
            .filter_map(|entry| entry.ok())
            .filter(|entry| {
                // 确保我们只处理文件
                if !entry.file_type().is_file() {
                    return false;
                }
                // 获取文件路径的扩展名
                match entry.path().extension() {
                    // 如果扩展名是 "jar" (不区分大小写)
                    Some(ext) => ext.to_string_lossy().eq_ignore_ascii_case("jar"),
                    None => false,
                }
            })
            // 将 PathBuf 转换为 String
            .map(|entry| entry.path().to_string_lossy().into_owned())
            .collect();

        let path_separator = ":";

        let hadoop_jars_path = jar_files.join(path_separator);

        // 3. 获取已有的 CLASSPATH 环境变量。
        // 如果 CLASSPATH 不存在，则返回一个空字符串。
        let existing_classpath = env::var("CLASSPATH").unwrap_or_else(|_| String::new());

        // 4. 组合新的路径和旧的路径。
        if hadoop_jars_path.is_empty() {
            // 如果没有找到任何 JAR 文件，则直接返回旧的 CLASSPATH
            existing_classpath
        } else if existing_classpath.is_empty() {
            // 如果旧的 CLASSPATH 为空，则只返回新的 JAR 路径
            hadoop_jars_path
        } else {
            // 将新的路径追加到旧的 CLASSPATH 前面
            format!("{}{}{}", hadoop_jars_path, path_separator, existing_classpath)
        }
    }


    /// 下载HDFS文件或目录到本地
    /// 如果hdfs_path是目录，则递归下载所有子文件到local_dir，保持目录结构
    /// 如果hdfs_path是文件，则直接下载到local_dir目录下
    pub async fn download(&self, hdfs_path: &str, local_dir: &str) -> Result<(), HdfsError> {
        info!("Starting download from HDFS path: {} to local: {}", hdfs_path, local_dir);

        // 检查HDFS路径是否存在
        let metadata = self.client.stat(hdfs_path).await
            .map_err(|e| HdfsError::FileNotFound(format!("HDFS path not found: {}, error: {}", hdfs_path, e)))?;

        // 确保本地目录存在
        fs::create_dir_all(local_dir)
            .map_err(|e| HdfsError::IOError(e))?;

        if metadata.mode() == EntryMode::FILE {
            // 下载单个文件
            let file_name = Path::new(hdfs_path).file_name()
                .ok_or_else(|| HdfsError::InvalidPath(format!("Invalid file path: {}", hdfs_path)))?
                .to_string_lossy();
            let local_file_path = Path::new(local_dir).join(file_name.as_ref());
            self.download_file(hdfs_path, &local_file_path.to_string_lossy()).await?;
        } else {
            // 递归下载目录
            self.download_directory(hdfs_path, local_dir).await?;
        }

        info!("Download completed successfully");
        Ok(())
    }

    /// 递归下载目录
    async fn download_directory(&self, hdfs_dir: &str, local_dir: &str) -> Result<(), HdfsError> {
        let normalized_dir = if !hdfs_dir.ends_with('/') {
            format!("{}/", hdfs_dir)
        } else {
            hdfs_dir.to_string()
        };
        let entries = self.client.list(&normalized_dir).await
            .map_err(|e| HdfsError::OperationFailed(format!("Failed to list directory: {}, error: {}", normalized_dir, e)))?;

        for entry in entries {
            let entry_path = format!("{}/{}", hdfs_dir.trim_end_matches('/'), entry.name());
            let local_entry_path = Path::new(local_dir).join(entry.name());

            if entry.metadata().mode() == EntryMode::DIR {
                if normalized_dir == format!("/{}/", entry.path().trim_start_matches('/').trim_end_matches('/')) {
                    continue;
                }

                // 创建本地子目录并递归下载
                fs::create_dir_all(&local_entry_path)
                    .map_err(|e| HdfsError::IOError(e))?;
                Box::pin(self.download_directory(&entry_path, &local_entry_path.to_string_lossy())).await?;
            } else {
                // 下载文件
                self.download_file(&entry_path, &local_entry_path.to_string_lossy()).await?;
            }
        }
        Ok(())
    }

    /// 下载单个文件
    async fn download_file(&self, hdfs_file: &str, local_file: &str) -> Result<(), HdfsError> {
        info!("Downloading file: {} -> {}", hdfs_file, local_file);

        // 使用流式下载方法来支持大文件
        self.download_file_streaming(hdfs_file, local_file).await
    }

    /// 上传本地文件或目录到HDFS
    /// 如果local_path是目录，则递归上传所有子文件到hdfs_dir，保持目录结构
    /// 如果local_path是文件，则直接上传到hdfs_dir目录下
    pub async fn upload(&self, local_path: &str, hdfs_dir: &str) -> Result<(), HdfsError> {
        info!("Starting upload from local path: {} to HDFS: {}", local_path, hdfs_dir);

        let local_metadata = fs::metadata(local_path)
            .map_err(|e| HdfsError::FileNotFound(format!("Local path not found: {}, error: {}", local_path, e)))?;

        if local_metadata.is_file() {
            // 上传单个文件
            let file_name = Path::new(local_path).file_name()
                .ok_or_else(|| HdfsError::InvalidPath(format!("Invalid file path: {}", local_path)))?
                .to_string_lossy();
            let hdfs_file_path = format!("{}/{}", hdfs_dir.trim_end_matches('/'), file_name);
            self.upload_file(local_path, &hdfs_file_path).await?;
        } else {
            // 递归上传目录
            self.upload_directory(local_path, hdfs_dir).await?;
        }

        info!("Upload completed successfully");
        Ok(())
    }

    /// 递归上传目录
    async fn upload_directory(&self, local_dir: &str, hdfs_dir: &str) -> Result<(), HdfsError> {
        let entries = fs::read_dir(local_dir)
            .map_err(|e| HdfsError::IOError(e))?;

        for entry in entries {
            let entry = entry.map_err(|e| HdfsError::IOError(e))?;
            let local_entry_path = entry.path();
            let entry_name = entry.file_name().to_string_lossy().to_string();
            let hdfs_entry_path = format!("{}/{}", hdfs_dir.trim_end_matches('/'), entry_name);

            if local_entry_path.is_dir() {
                // 递归上传子目录
                Box::pin(self.upload_directory(&local_entry_path.to_string_lossy(), &hdfs_entry_path)).await?;
            } else {
                // 上传文件
                self.upload_file(&local_entry_path.to_string_lossy(), &hdfs_entry_path).await?;
            }
        }
        Ok(())
    }

    /// 上传单个文件（支持大文件流式上传）
    /// 使用流式写入避免将整个文件加载到内存中
    async fn upload_file(&self, local_file: &str, hdfs_file: &str) -> Result<(), HdfsError> {
        info!("Streaming upload to HDFS: {} -> {}", local_file, hdfs_file);

        // 打开本地文件
        let mut file = fs::File::open(local_file)
            .map_err(|e| HdfsError::IOError(e))?;

        // 获取文件大小
        let file_size = file.metadata()
            .map_err(|e| HdfsError::IOError(e))?
            .len();

        // 分块读取和上传，避免内存占用过大
        const CHUNK_SIZE: usize = 8 * 1024 * 1024; // 8MB chunks
        let mut buffer = vec![0; CHUNK_SIZE];
        let mut offset = 0u64;

        let mut hdfs_writer = self.client.writer_with(hdfs_file)
            .chunk(CHUNK_SIZE)
            .concurrent(8)
            .await
            .map_err(|e| HdfsError::DataTransferError(format!("Failed to create writer to HDFS: {}, error: {}", hdfs_file, e)))?;

        while offset < file_size {
            // 读取一个数据块
            let bytes_read = std::io::Read::read(&mut file, &mut buffer)
                .map_err(|e| HdfsError::IOError(e))?;

            if bytes_read == 0 {
                break; // 文件读取完毕
            }

            // 写入数据块到HDFS - 创建拥有的数据副本
            let chunk_data = buffer[0..bytes_read].to_vec();
            hdfs_writer.write(opendal::Buffer::from(chunk_data))
                .await
                .map_err(|e| HdfsError::DataTransferError(
                    format!("Failed to write chunk to HDFS: {}, error: {}", hdfs_file, e)
                ))?;

            offset += bytes_read as u64;
            info!("Uploaded {} bytes ({:.2}%) to {}", offset, (offset as f64 / file_size as f64) * 100.0, hdfs_file);
        }

        // 关闭writer确保所有数据都已写入
        hdfs_writer.close()
            .await
            .map_err(|e| HdfsError::DataTransferError(
                format!("Failed to close HDFS writer: {}, error: {}", hdfs_file, e)
            ))?;

        info!("Successfully uploaded {} bytes to {}", offset, hdfs_file);
        Ok(())
    }

    /// 直接读取HDFS文件或目录中的Parquet文件
    /// 如果hdfs_path是目录，则递归读取所有.parquet文件
    /// 如果hdfs_path是文件，则直接读取该文件
    // pub async fn read_parquet(&self, hdfs_path: &str) -> Result<Vec<RecordBatch>, HdfsError> {
    //     info!("Reading Parquet data from HDFS path: {}", hdfs_path);
    //
    //     let metadata = self.client.stat(hdfs_path).await
    //         .map_err(|e| HdfsError::FileNotFound(format!("HDFS path not found: {}, error: {}", hdfs_path, e)))?;
    //
    //     let mut all_batches = Vec::new();
    //
    //     if metadata.mode() == EntryMode::FILE {
    //         // 读取单个文件
    //         if hdfs_path.to_lowercase().ends_with(".parquet") {
    //             let batches = self.read_parquet_file(hdfs_path).await?;
    //             all_batches.extend(batches);
    //         } else {
    //             warn!("File {} is not a Parquet file, skipping", hdfs_path);
    //         }
    //     } else {
    //         // 递归读取目录中的所有Parquet文件
    //         let parquet_files = self.find_parquet_files(hdfs_path).await?;
    //         for file_path in parquet_files {
    //             let batches = self.read_parquet_file(&file_path).await?;
    //             all_batches.extend(batches);
    //         }
    //     }
    //
    //     info!("Successfully read {} record batches", all_batches.len());
    //     Ok(all_batches)
    // }

    /// 递归查找目录中的所有Parquet文件
    // async fn find_parquet_files(&self, hdfs_dir: &str) -> Result<Vec<String>, HdfsError> {
    //     let mut parquet_files = Vec::new();
    //     self.collect_parquet_files(hdfs_dir, &mut parquet_files).await?;
    //     Ok(parquet_files)
    // }

    /// 递归收集Parquet文件路径
    // async fn collect_parquet_files(&self, hdfs_dir: &str, parquet_files: &mut Vec<String>) -> Result<(), HdfsError> {
    //     let entries = self.client.list(hdfs_dir).await
    //         .map_err(|e| HdfsError::OperationFailed(format!("Failed to list directory: {}, error: {}", hdfs_dir, e)))?;
    //
    //     for entry in entries {
    //         let entry_path = format!("{}/{}", hdfs_dir.trim_end_matches('/'), entry.name());
    //
    //         if entry.metadata().mode() == EntryMode::DIR {
    //             // 递归处理子目录
    //             self.collect_parquet_files(&entry_path, parquet_files).await?;
    //         } else if entry.name().to_lowercase().ends_with(".parquet") {
    //             // 添加Parquet文件
    //             parquet_files.push(entry_path);
    //         }
    //     }
    //     Ok(())
    // }

    /// 读取单个Parquet文件
    // async fn read_parquet_file(&self, hdfs_file: &str) -> Result<Vec<RecordBatch>, HdfsError> {
    //     debug!("Reading Parquet file: {}", hdfs_file);
    //
    //     let data = self.client.reader(hdfs_file).await
    //         .map_err(|e| HdfsError::DataTransferError(format!("Failed to read HDFS file: {}, error: {}", hdfs_file, e)))?;
    //
    //     let cursor = Cursor::new(data);
    //     let file_reader = SerializedFileReader::new(cursor)
    //         .map_err(|e| HdfsError::DataTransferError(format!("Failed to create Parquet reader: {}", e)))?;
    //
    //     let mut arrow_reader = ParquetFileArrowReader::new(Arc::new(file_reader));
    //     let record_batch_reader = arrow_reader.get_record_reader(2048)
    //         .map_err(|e| HdfsError::DataTransferError(format!("Failed to create record batch reader: {}", e)))?;
    //
    //     let mut batches = Vec::new();
    //     for batch_result in record_batch_reader {
    //         let batch = batch_result
    //             .map_err(|e| HdfsError::DataTransferError(format!("Failed to read record batch: {}", e)))?;
    //         batches.push(batch);
    //     }
    //
    //     debug!("Successfully read {} batches from {}", batches.len(), hdfs_file);
    //     Ok(batches)
    // }

    /// 直接写入数据到HDFS文件（覆盖写入）
    pub async fn write_data(&self, hdfs_file: &str, data: Vec<u8>) -> Result<(), HdfsError> {
        info!("Writing data to HDFS file: {}", hdfs_file);

        self.client.write(hdfs_file, opendal::Buffer::from(data)).await
            .map_err(|e| HdfsError::DataTransferError(format!("Failed to write HDFS file: {}, error: {}", hdfs_file, e)))?;

        info!("Data written successfully to: {}", hdfs_file);
        Ok(())
    }

    /// 删除HDFS文件或目录
    /// 如果是目录且非空，会递归删除所有内容
    pub async fn delete(&self, hdfs_path: &str) -> Result<(), HdfsError> {
        info!("Deleting HDFS path: {}", hdfs_path);

        // 检查路径是否存在
        if !self.exists(hdfs_path).await? {
            info!("Path does not exist, nothing to delete: {}", hdfs_path);
            return Ok(());
        }

        // 获取路径信息
        let metadata = self.client.stat(hdfs_path).await
            .map_err(|e| HdfsError::OperationFailed(format!("Failed to stat path: {}, error: {}", hdfs_path, e)))?;

        if metadata.mode() == EntryMode::FILE {
            // 删除文件
            self.client.delete(hdfs_path).await
                .map_err(|e| HdfsError::OperationFailed(format!("Failed to delete file: {}, error: {}", hdfs_path, e)))?;
        } else {
            // 递归删除目录
            self.delete_directory_recursive(hdfs_path).await?;
        }

        info!("Successfully deleted: {}", hdfs_path);
        Ok(())
    }

    /// 递归删除目录及其所有内容
    async fn delete_directory_recursive(&self, hdfs_dir: &str) -> Result<(), HdfsError> {
        let normalized_dir = if !hdfs_dir.ends_with('/') {
            format!("{}/", hdfs_dir)
        } else {
            hdfs_dir.to_string()
        };

        // 列出目录中的所有条目
        let entries = self.client.list(&normalized_dir).await
            .map_err(|e| HdfsError::OperationFailed(format!("Failed to list directory for deletion: {}, error: {}", normalized_dir, e)))?;

        // 先删除所有子项
        for entry in entries {
            if normalized_dir == format!("/{}/", entry.path().trim_start_matches('/').trim_end_matches('/')) {
                continue;
            }
            let entry_path = format!("{}/{}", hdfs_dir.trim_end_matches('/'), entry.name());
            
            if entry.metadata().mode() == EntryMode::DIR {
                // 递归删除子目录
                Box::pin(self.delete_directory_recursive(&entry_path)).await?;
            } else {
                // 删除文件
                self.client.delete(&entry_path).await
                    .map_err(|e| HdfsError::OperationFailed(format!("Failed to delete file: {}, error: {}", entry_path, e)))?;
                info!("Deleted file: {}", entry_path);
            }
        }

        // 最后删除空目录
        self.client.delete(hdfs_dir).await
            .map_err(|e| HdfsError::OperationFailed(format!("Failed to delete empty directory: {}, error: {}", hdfs_dir, e)))?;
        
        info!("Deleted directory: {}", hdfs_dir);
        Ok(())
    }

    /// 列出HDFS目录或文件
    /// 如果给定的是目录路径，则递归列出所有子孙文件
    /// 如果给定的是文件路径，则只返回该文件信息
    pub async fn list_files(&self, hdfs_path: &str, recursive: bool) -> Result<Vec<String>, HdfsError> {
        info!("Listing HDFS path: {}", hdfs_path);

        // 首先检查路径是否存在
        if !self.exists(hdfs_path).await? {
            return Err(HdfsError::FileNotFound(format!("Path not found: {}", hdfs_path)));
        }

        let mut files = Vec::new();

        // 获取路径的元数据来判断是文件还是目录
        let stat = self.client.stat(hdfs_path).await
            .map_err(|e| HdfsError::OperationFailed(format!("Failed to stat path: {}, recursive: {}, error: {}", hdfs_path, recursive, e)))?;

        if stat.mode() == EntryMode::FILE {
            // 如果是文件，直接返回该文件路径
            files.push(hdfs_path.to_string());
        } else {
            // 如果是目录，递归列出所有文件
            self.collect_all_files(hdfs_path, &mut files, recursive).await?;
        }

        info!("Found {} files under path: {}", files.len(), hdfs_path);
        Ok(files)
    }

    /// 递归收集目录下的所有文件
    async fn collect_all_files(&self, hdfs_dir: &str, files: &mut Vec<String>, recursive: bool) -> Result<(), HdfsError> {
        let normalized_dir = if !hdfs_dir.ends_with('/') {
            format!("{}/", hdfs_dir)
        } else {
            hdfs_dir.to_string()
        };
        let entries = self.client.list_with(&normalized_dir).recursive(recursive).await
            .map_err(|e| HdfsError::OperationFailed(format!("Failed to list directory: {}, recursive: {}, error: {}", normalized_dir, recursive, e)))?;

        for entry in entries {
            if entry.metadata().mode() == EntryMode::FILE {
                // 添加文件路径
                files.push(format!("/{}",entry.path().to_string().trim_start_matches('/')));
            }
        }
        Ok(())
    }

    /// 检查给定的路径或文件在HDFS上是否存在
    pub async fn exists(&self, hdfs_path: &str) -> Result<bool, HdfsError> {
        info!("Checking if HDFS path exists: {}", hdfs_path);
        let stat = self.client.stat(hdfs_path).await;
        match stat {
            Ok(_) => {
                info!("Path exists: {}", hdfs_path);
                Ok(true)
            },
            Err(e) => {
                // 检查错误类型，如果是文件不存在的错误，返回false
                // 其他错误则传播
                let error_msg = e.to_string().to_lowercase();
                if error_msg.contains("not found") || error_msg.contains("no such file") {
                    info!("Path does not exist: {}", hdfs_path);
                    Ok(false)
                } else {
                    Err(HdfsError::OperationFailed(format!("Failed to check path existence: {}, error: {}", hdfs_path, e)))
                }
            }
        }
    }

    /// 下载单个文件（支持大文件流式下载）
    /// 使用流式读取避免将整个文件加载到内存中
    pub async fn download_file_streaming(&self, hdfs_file: &str, local_file: &str) -> Result<(), HdfsError> {
        info!("Streaming download from HDFS: {} -> {}", hdfs_file, local_file);

        // 确保本地目录存在
        if let Some(parent) = Path::new(local_file).parent() {
            fs::create_dir_all(parent)
                .map_err(|e| HdfsError::IOError(e))?;
        }

        // 获取文件大小
        let meta = self.client.stat(hdfs_file).await
        .map_err(|e| HdfsError::OperationFailed(format!("Failed to stat path: {}, error: {}", hdfs_file, e)))?;
        let file_size = meta.content_length();

        // 创建本地文件
        let mut local_file_handle = fs::File::create(local_file)
            .map_err(|e| HdfsError::IOError(e))?;

        // 分块读取和写入，避免内存占用过大
        const CHUNK_SIZE: usize = 8 * 1024 * 1024; // 8MB chunks
        let mut offset = 0u64;
        let hdfs_reader = self.client.reader_with(hdfs_file).await
        .map_err(|e| HdfsError::DataTransferError(format!("Failed to create reader from HDFS: {}, error: {}", hdfs_file, e)))?;
        while offset < file_size {
            let end = std::cmp::min(offset + CHUNK_SIZE as u64, file_size);
            let chunk = hdfs_reader.read(offset..end).await
            .map_err(|e| HdfsError::DataTransferError(format!("Failed to read chunk from HDFS: {}, error: {}", hdfs_file, e)))?;

            if chunk.is_empty() {
                break; // 文件读取完毕
            }

            // 写入本地文件
            local_file_handle.write_all(&chunk.to_bytes())
                .map_err(|e| HdfsError::IOError(e))?;

            offset += chunk.len() as u64;
            info!("Downloaded {} bytes from {}", offset, hdfs_file);
        }

        local_file_handle.flush()
            .map_err(|e| HdfsError::IOError(e))?;

        info!("Successfully downloaded {} bytes from {} to {}", offset, hdfs_file, local_file);
        Ok(())
    }
}
