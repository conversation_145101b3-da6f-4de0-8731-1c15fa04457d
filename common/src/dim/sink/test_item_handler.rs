use crate::ck::ck_sink::Sink<PERSON><PERSON><PERSON>;
use crate::model::constant::{CLUSTER_NAME, CLUSTER_TABLE, LOCAL_TABLE};

const TABLE_NAME: &str = "dim_test_item{CLUSTER}";
const PARTITION_EXPR: &str = "('{CUSTOMER}', '{UPLOAD_TYPE}', '{TEST_AREA}', '{FACTORY}', '{SUB_CUSTOMER}', '{DEVICE_ID}')";

/// Sink handler for TestItem data to ClickHouse
/// <PERSON><PERSON> writes to dim_test_item_local table
#[derive(Debug, Clone)]
pub struct TestItemHandler {
    pub db_name: String,
    pub table_name: String,
}

impl TestItemHandler {
    /// Creates a new TestItemHandler
    ///
    /// # Arguments
    /// * `db_name` - The database name for the DIM layer
    pub fn new(db_name: String, insert_cluster_table: bool) -> Self {
        let table_name = if insert_cluster_table {
            TABLE_NAME.replace(CLUSTER_NAME, CLUSTER_TABLE)
        } else {
            TABLE_NAME.replace(CLUSTER_NAME, LOCAL_TABLE)
        };
        Self { db_name, table_name }
    }
}

impl SinkHandler for TestItemHandler {
    fn db_name(&self) -> &str {
        &self.db_name
    }

    fn table_name(&self) -> &str {
        &self.table_name
    }

    fn partition_expr(&self) -> &str {
        PARTITION_EXPR
    }
}
