use crate::ck::ck_sink::SinkHand<PERSON>;

const TABLE_NAME: &str = "dim_test_program_test_item_local";
const PARTITION_EXPR: &str = "('{CUSTOMER}', '{UPLOAD_TYPE}', '{TEST_AREA}', '{FACTORY}', '{SUB_CUSTOMER}', '{DEVICE_ID}')";

/// Sink handler for TestProgramTestItem data to ClickHouse
/// <PERSON><PERSON> writes to dim_test_program_test_item_local table
#[derive(Debug, Clone)]
pub struct TestProgramTestItemHandler {
    pub db_name: String,
    pub table_name: String,
}

impl TestProgramTestItemHandler {
    /// Creates a new TestProgramTestItemHandler
    ///
    /// # Arguments
    /// * `db_name` - The database name for the DIM layer
    pub fn new(db_name: String) -> Self {
        Self {
            db_name,
            table_name: TABLE_NAME.into()
        }
    }

}

impl SinkHandler for TestProgramTestItemHandler {
    fn db_name(&self) -> &str {
        &self.db_name
    }

    fn table_name(&self) -> &str {
        &self.table_name
    }

    fn partition_expr(&self) -> &str {
        PARTITION_EXPR
    }
}
