use clickhouse::Row;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use chrono::{DateTime, Utc};
use crate::utils::date::IntoDateTimeUtc;

use super::test_program_test_order::TestProgramTestOrder;

#[derive(Row, Debug, Clone, Serialize, Deserialize, PartialEq)]
#[allow(non_snake_case)]
pub struct TestProgramTestOrderRow {
    pub CUSTOMER: String,
    pub SUB_CUSTOMER: String,
    pub UPLOAD_TYPE: String,
    pub FACTORY: String,
    pub FACTORY_SITE: String,
    pub FAB: String,
    pub FAB_SITE: String,
    pub TEST_AREA: String,
    pub TEST_STAGE: String,
    pub DEVICE_ID: String,
    pub LOT_TYPE: String,
    pub TEST_PROGRAM: String,
    pub TEST_PROGRAM_VERSION: String,
    pub TEST_NUM: Option<u32>,
    pub TEST_TXT: String,
    pub TEST_ITEM: String,
    pub TESTITEM_TYPE: String,
    pub TEST_ORDER: Option<u32>,
    pub EXTRA_INFO: Vec<(String, String)>,
    pub CREATE_HOUR_KEY: String,
    pub CREATE_DAY_KEY: String,
    #[serde(with = "clickhouse::serde::chrono::datetime")]
    pub CREATE_TIME: DateTime<Utc>,
    pub CREATE_USER: String,
    #[serde(with = "clickhouse::serde::chrono::datetime")]
    pub UPLOAD_TIME: DateTime<Utc>,
    pub IS_DELETE: u8,
}

impl TestProgramTestOrderRow {
    pub fn new(test_program_test_order: &TestProgramTestOrder) -> Self {
        let now = Utc::now();
        Self {
            CUSTOMER: test_program_test_order.CUSTOMER.clone().unwrap_or_default(),
            SUB_CUSTOMER: test_program_test_order.SUB_CUSTOMER.clone().unwrap_or_default(),
            UPLOAD_TYPE: test_program_test_order.UPLOAD_TYPE.clone().unwrap_or_default(),
            FACTORY: test_program_test_order.FACTORY.clone().unwrap_or_default(),
            FACTORY_SITE: test_program_test_order.FACTORY_SITE.clone().unwrap_or_default(),
            FAB: test_program_test_order.FAB.clone().unwrap_or_default(),
            FAB_SITE: test_program_test_order.FAB_SITE.clone().unwrap_or_default(),
            TEST_AREA: test_program_test_order.TEST_AREA.clone().unwrap_or_default(),
            TEST_STAGE: test_program_test_order.TEST_STAGE.clone().unwrap_or_default(),
            DEVICE_ID: test_program_test_order.DEVICE_ID.clone().unwrap_or_default(),
            LOT_TYPE: test_program_test_order.LOT_TYPE.clone().unwrap_or_default(),
            TEST_PROGRAM: test_program_test_order.TEST_PROGRAM.clone().unwrap_or_default(),
            TEST_PROGRAM_VERSION: test_program_test_order.TEST_PROGRAM_VERSION.clone().unwrap_or_default(),
            TEST_NUM: test_program_test_order.TEST_NUM.map(|v| v as u32),
            TEST_TXT: test_program_test_order.TEST_TXT.clone().unwrap_or_default(),
            TEST_ITEM: test_program_test_order.TEST_ITEM.clone().unwrap_or_default(),
            TESTITEM_TYPE: test_program_test_order.TESTITEM_TYPE.clone().unwrap_or_default(),
            TEST_ORDER: test_program_test_order.TEST_ORDER.map(|v| v as u32),
            EXTRA_INFO: test_program_test_order.EXTRA_INFO.as_ref()
                .map(|map| map.iter().map(|(k, v)| (k.clone(), v.clone())).collect())
                .unwrap_or_default(),
            CREATE_HOUR_KEY: test_program_test_order.CREATE_HOUR_KEY.clone().unwrap_or_default(),
            CREATE_DAY_KEY: test_program_test_order.CREATE_DAY_KEY.clone().unwrap_or_default(),
            CREATE_TIME: test_program_test_order.CREATE_TIME.and_then(|ts| Some(ts.into_utc())).unwrap_or(now),
            CREATE_USER: test_program_test_order.CREATE_USER.clone().unwrap_or_default(),
            UPLOAD_TIME: test_program_test_order.UPLOAD_TIME.and_then(|ts| Some(ts.into_utc())).unwrap_or(now),
            IS_DELETE: 0,
        }
    }
}

impl Default for TestProgramTestOrderRow {
    fn default() -> Self {
        Self::new(&TestProgramTestOrder::new())
    }
}
