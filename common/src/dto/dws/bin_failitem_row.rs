use clickhouse::Row;
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};
use crate::utils::date::IntoDateTimeUtc;

use super::bin_failitem::BinFailitem;

#[derive(Row, Debug, Clone, Serialize, Deserialize, PartialEq)]
#[allow(non_snake_case)]
pub struct BinFailitemRow {
    pub CUSTOMER: String,
    pub SUB_CUSTOMER: String,
    pub UPLOAD_TYPE: String,
    pub FACTORY: String,
    pub FACTORY_SITE: String,
    pub FAB: String,
    pub FAB_SITE: String,
    pub TEST_AREA: String,
    pub TEST_STAGE: String,
    pub DEVICE_ID: String,
    pub LOT_TYPE: String,
    pub TEST_PROGRAM: String,
    pub TEST_PROGRAM_VERSION: String,
    pub TEST_NUM: Option<u32>,
    pub TEST_TXT: String,
    pub TEST_ITEM: String,
    pub TESTITEM_TYPE: String,
    pub UNITS: String,
    pub ORIGIN_UNITS: String,
    pub HBIN_NAM: String,
    pub HBIN_PF: String,
    pub HBIN: String,
    pub HBIN_NUM: Option<u32>,
    pub HBIN_NUM_KEY: String,
    pub SBIN_NUM: Option<u32>,
    pub SBIN_NUM_KEY: String,
    pub SBIN_NAM: String,
    pub SBIN_PF: String,
    pub SBIN: String,
    pub CREATE_HOUR_KEY: String,
    pub CREATE_DAY_KEY: String,
    #[serde(with = "clickhouse::serde::chrono::datetime")]
    pub CREATE_TIME: DateTime<Utc>,
    pub CREATE_USER: String,
    #[serde(with = "clickhouse::serde::chrono::datetime")]
    pub UPLOAD_TIME: DateTime<Utc>,
    pub VERSION: u32,
    pub IS_DELETE: u8,
}

impl BinFailitemRow {
    pub fn new(bin_failitem: &BinFailitem) -> Self {
        let now = Utc::now();
        Self {
            CUSTOMER: bin_failitem.CUSTOMER.clone().unwrap_or_default(),
            SUB_CUSTOMER: bin_failitem.SUB_CUSTOMER.clone().unwrap_or_default(),
            UPLOAD_TYPE: bin_failitem.UPLOAD_TYPE.clone().unwrap_or_default(),
            FACTORY: bin_failitem.FACTORY.clone().unwrap_or_default(),
            FACTORY_SITE: bin_failitem.FACTORY_SITE.clone().unwrap_or_default(),
            FAB: bin_failitem.FAB.clone().unwrap_or_default(),
            FAB_SITE: bin_failitem.FAB_SITE.clone().unwrap_or_default(),
            TEST_AREA: bin_failitem.TEST_AREA.clone().unwrap_or_default(),
            TEST_STAGE: bin_failitem.TEST_STAGE.clone().unwrap_or_default(),
            DEVICE_ID: bin_failitem.DEVICE_ID.clone().unwrap_or_default(),
            LOT_TYPE: bin_failitem.LOT_TYPE.clone().unwrap_or_default(),
            TEST_PROGRAM: bin_failitem.TEST_PROGRAM.clone().unwrap_or_default(),
            TEST_PROGRAM_VERSION: bin_failitem.TEST_PROGRAM_VERSION.clone().unwrap_or_default(),
            TEST_NUM: bin_failitem.TEST_NUM.map(|v| v as u32),
            TEST_TXT: bin_failitem.TEST_TXT.clone().unwrap_or_default(),
            TEST_ITEM: bin_failitem.TEST_ITEM.clone().unwrap_or_default(),
            TESTITEM_TYPE: bin_failitem.TESTITEM_TYPE.clone().unwrap_or_default(),
            UNITS: bin_failitem.UNITS.clone().unwrap_or_default(),
            ORIGIN_UNITS: bin_failitem.ORIGIN_UNITS.clone().unwrap_or_default(),
            HBIN_NAM: bin_failitem.HBIN_NAM.clone().unwrap_or_default(),
            HBIN_PF: bin_failitem.HBIN_PF.clone().unwrap_or_default(),
            HBIN: bin_failitem.HBIN.clone().unwrap_or_default(),
            HBIN_NUM: bin_failitem.HBIN_NUM.map(|v| v as u32),
            HBIN_NUM_KEY: bin_failitem.HBIN_NUM_KEY.clone().unwrap_or_default(),
            SBIN_NUM: bin_failitem.SBIN_NUM.map(|v| v as u32),
            SBIN_NUM_KEY: bin_failitem.SBIN_NUM_KEY.clone().unwrap_or_default(),
            SBIN_NAM: bin_failitem.SBIN_NAM.clone().unwrap_or_default(),
            SBIN_PF: bin_failitem.SBIN_PF.clone().unwrap_or_default(),
            SBIN: bin_failitem.SBIN.clone().unwrap_or_default(),
            CREATE_HOUR_KEY: bin_failitem.CREATE_HOUR_KEY.clone().unwrap_or_default(),
            CREATE_DAY_KEY: bin_failitem.CREATE_DAY_KEY.clone().unwrap_or_default(),
            CREATE_TIME: bin_failitem.CREATE_TIME.and_then(|ts| Some(ts.into_utc())).unwrap_or(now),
            CREATE_USER: bin_failitem.CREATE_USER.clone().unwrap_or_default(),
            UPLOAD_TIME: bin_failitem.UPLOAD_TIME.and_then(|ts| Some(ts.into_utc())).unwrap_or(now),
            VERSION: bin_failitem.VERSION.unwrap_or(now.timestamp_millis()) as u32,
            IS_DELETE: 0,
        }
    }
}

impl Default for BinFailitemRow {
    fn default() -> Self {
        Self::new(&BinFailitem::new())
    }
}
