use serde::{Deserialize, Serialize};

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct BinRelation {
    #[serde(rename = "hbinNum")]
    pub hbin_num: Option<i64>,
    #[serde(rename = "hbinNam")]
    pub hbin_nam: Option<String>,
    #[serde(rename = "hbinPf")]
    pub hbin_pf: Option<String>,
    pub hbin: Option<String>,
    #[serde(rename = "sbinNum")]
    pub sbin_num: Option<i64>,
    #[serde(rename = "sbinNam")]
    pub sbin_nam: Option<String>,
    #[serde(rename = "sbinPf")]
    pub sbin_pf: Option<String>,
    pub sbin: Option<String>,
}