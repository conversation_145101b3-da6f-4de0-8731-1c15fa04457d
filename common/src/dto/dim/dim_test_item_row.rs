use crate::dto::dim::DimTestItem;
use crate::utils::date::IntoDateTimeUtc;
use crate::utils::decimal::{Decimal38_18, IntoDecimal38_18};
use chrono::{DateTime, Utc};
use clickhouse::Row;
use serde::{Deserialize, Serialize};
use std::sync::Arc;

/// DimTestItemRow represents a row in the dim_test_item_local ClickHouse table
/// Corresponds to the dim_test_item_local table schema
#[derive(Debug, Clone, Serialize, Deserialize, Row)]
#[allow(non_snake_case)]
pub struct DimTestItemRow {
    pub CUSTOMER: Arc<str>,
    pub SUB_CUSTOMER: Arc<str>,
    pub UPLOAD_TYPE: Arc<str>,
    pub FILE_ID: u32,
    pub FILE_NAME: Arc<str>,
    pub FACTORY: Arc<str>,
    pub FACTORY_SITE: Arc<str>,
    pub FAB: Arc<str>,
    pub FAB_SITE: Arc<str>,
    pub TEST_AREA: Arc<str>,
    pub TEST_STAGE: Arc<str>,
    pub LOT_TYPE: Arc<str>,
    pub DEVICE_ID: Arc<str>,
    pub LOT_ID: Arc<str>,
    pub SBLOT_ID: Arc<str>,
    pub PROCESS: Arc<str>,
    pub WAFER_ID: Arc<str>,
    pub WAFER_ID_KEY: Arc<str>,
    pub WAFER_NO: Arc<str>,
    pub WAFER_NO_KEY: Arc<str>,
    pub WAFER_LOT_ID: Arc<str>,
    pub FABWF_ID: Arc<str>,
    pub TEST_PROGRAM: Arc<str>,
    pub TEST_PROGRAM_VERSION: Arc<str>,
    pub TEST_TEMPERATURE: Arc<str>,
    pub TESTER_NAME: Arc<str>,
    pub TESTER_TYPE: Arc<str>,
    pub OPERATOR_NAME: Arc<str>,
    pub PROBER_HANDLER_TYP: Arc<str>,
    pub PROBER_HANDLER_ID: Arc<str>,
    pub PROBECARD_LOADBOARD_TYP: Arc<str>,
    pub PROBECARD_LOADBOARD_ID: Arc<str>,
    #[serde(with = "clickhouse::serde::chrono::datetime::option")]
    pub START_TIME: Option<DateTime<Utc>>,
    #[serde(with = "clickhouse::serde::chrono::datetime::option")]
    pub END_TIME: Option<DateTime<Utc>>,
    pub START_HOUR_KEY: Arc<str>,
    pub START_DAY_KEY: Arc<str>,
    pub END_HOUR_KEY: Arc<str>,
    pub END_DAY_KEY: Arc<str>,
    pub TEST_HEAD: Option<u32>,
    pub SITE: Option<u32>,
    pub SITE_KEY: Arc<str>,
    pub HBIN_NUM: Option<u32>,
    pub HBIN_NUM_KEY: Arc<str>,
    pub SBIN_NUM: Option<u32>,
    pub SBIN_NUM_KEY: Arc<str>,
    pub SBIN_PF: Arc<str>,
    pub SBIN_NAM: Arc<str>,
    pub HBIN_PF: Arc<str>,
    pub HBIN_NAM: Arc<str>,
    pub HBIN: Arc<str>,
    pub SBIN: Arc<str>,
    pub TEST_NUM: Option<u32>,
    pub TEST_TXT: Arc<str>,
    pub TEST_ITEM: Arc<str>,
    pub TESTITEM_TYPE: Arc<str>,
    pub ORIGIN_HI_LIMIT: Option<Decimal38_18>,
    pub ORIGIN_LO_LIMIT: Option<Decimal38_18>,
    pub ORIGIN_UNITS: Arc<str>,
    pub LO_LIMIT: Option<Decimal38_18>,
    pub HI_LIMIT: Option<Decimal38_18>,
    pub UNITS: Arc<str>,
    pub CONDITION_SET: Vec<(Arc<str>, Arc<str>)>,
    pub CREATE_HOUR_KEY: Arc<str>,
    pub CREATE_DAY_KEY: Arc<str>,
    #[serde(with = "clickhouse::serde::chrono::datetime")]
    pub CREATE_TIME: DateTime<Utc>,
    pub CREATE_USER: Arc<str>,
    #[serde(with = "clickhouse::serde::chrono::datetime")]
    pub UPLOAD_TIME: DateTime<Utc>,
    pub VERSION: i64,
    pub IS_DELETE: u8,
}

impl DimTestItemRow {
    /// Convert DimTestItem to DimTestItemRow
    /// This implements the rich domain model pattern where conversion logic is in the Row model
    pub fn from_hdfs_entity(dim_test_item: &DimTestItem) -> Self {
        Self {
            CUSTOMER: dim_test_item.CUSTOMER.clone(),
            SUB_CUSTOMER: dim_test_item.SUB_CUSTOMER.clone(),
            UPLOAD_TYPE: dim_test_item.UPLOAD_TYPE.clone(),
            FILE_ID: dim_test_item.FILE_ID,
            FILE_NAME: dim_test_item.FILE_NAME.clone(),
            FACTORY: dim_test_item.FACTORY.clone(),
            FACTORY_SITE: dim_test_item.FACTORY_SITE.clone(),
            FAB: dim_test_item.FAB.clone(),
            FAB_SITE: dim_test_item.FAB_SITE.clone(),
            TEST_AREA: dim_test_item.TEST_AREA.clone(),
            TEST_STAGE: dim_test_item.TEST_STAGE.clone(),
            LOT_TYPE: dim_test_item.LOT_TYPE.clone(),
            DEVICE_ID: dim_test_item.DEVICE_ID.clone(),
            LOT_ID: dim_test_item.LOT_ID.clone(),
            SBLOT_ID: dim_test_item.SBLOT_ID.clone(),
            PROCESS: dim_test_item.PROCESS.clone(),
            WAFER_ID: dim_test_item.WAFER_ID.clone(),
            WAFER_ID_KEY: dim_test_item.WAFER_ID_KEY.clone(),
            WAFER_NO: dim_test_item.WAFER_NO.clone(),
            WAFER_NO_KEY: dim_test_item.WAFER_NO_KEY.clone(),
            WAFER_LOT_ID: dim_test_item.WAFER_LOT_ID.clone(),
            FABWF_ID: dim_test_item.FABWF_ID.clone(),
            TEST_PROGRAM: dim_test_item.TEST_PROGRAM.clone(),
            TEST_PROGRAM_VERSION: dim_test_item.TEST_PROGRAM_VERSION.clone(),
            TEST_TEMPERATURE: dim_test_item.TEST_TEMPERATURE.clone(),
            TESTER_NAME: dim_test_item.TESTER_NAME.clone(),
            TESTER_TYPE: dim_test_item.TESTER_TYPE.clone(),
            OPERATOR_NAME: dim_test_item.OPERATOR_NAME.clone(),
            PROBER_HANDLER_TYP: dim_test_item.PROBER_HANDLER_TYP.clone(),
            PROBER_HANDLER_ID: dim_test_item.PROBER_HANDLER_ID.clone(),
            PROBECARD_LOADBOARD_TYP: dim_test_item.PROBECARD_LOADBOARD_TYP.clone(),
            PROBECARD_LOADBOARD_ID: dim_test_item.PROBECARD_LOADBOARD_ID.clone(),
            START_TIME: dim_test_item
                .START_TIME
                .and_then(|ts| chrono::DateTime::from_timestamp_millis(ts)),
            END_TIME: dim_test_item
                .END_TIME
                .and_then(|ts| chrono::DateTime::from_timestamp_millis(ts)),
            START_HOUR_KEY: dim_test_item.START_HOUR_KEY.clone(),
            START_DAY_KEY: dim_test_item.START_DAY_KEY.clone(),
            END_HOUR_KEY: dim_test_item.END_HOUR_KEY.clone(),
            END_DAY_KEY: dim_test_item.END_DAY_KEY.clone(),
            TEST_HEAD: dim_test_item.TEST_HEAD,
            SITE: dim_test_item.SITE,
            SITE_KEY: dim_test_item.SITE_KEY.clone(),
            HBIN_NUM: dim_test_item.HBIN_NUM,
            HBIN_NUM_KEY: dim_test_item.HBIN_NUM_KEY.clone(),
            SBIN_NUM: dim_test_item.SBIN_NUM,
            SBIN_NUM_KEY: dim_test_item.SBIN_NUM_KEY.clone(),
            SBIN_PF: dim_test_item.SBIN_PF.clone(),
            SBIN_NAM: dim_test_item.SBIN_NAM.clone(),
            HBIN_PF: dim_test_item.HBIN_PF.clone(),
            HBIN_NAM: dim_test_item.HBIN_NAM.clone(),
            HBIN: dim_test_item.HBIN.clone(),
            SBIN: dim_test_item.SBIN.clone(),
            TEST_NUM: dim_test_item.TEST_NUM,
            TEST_TXT: dim_test_item.TEST_TXT.clone(),
            TEST_ITEM: dim_test_item.TEST_ITEM.clone(),
            TESTITEM_TYPE: dim_test_item.TESTITEM_TYPE.clone(),
            ORIGIN_HI_LIMIT: dim_test_item.ORIGIN_HI_LIMIT.map(|v| v.into_decimal38_18()),
            ORIGIN_LO_LIMIT: dim_test_item.ORIGIN_LO_LIMIT.map(|v| v.into_decimal38_18()),
            ORIGIN_UNITS: dim_test_item.ORIGIN_UNITS.clone(),
            LO_LIMIT: dim_test_item.LO_LIMIT.map(|v| v.into_decimal38_18()),
            HI_LIMIT: dim_test_item.HI_LIMIT.map(|v| v.into_decimal38_18()),
            UNITS: dim_test_item.UNITS.clone(),
            CONDITION_SET: dim_test_item.CONDITION_SET.clone(),
            CREATE_HOUR_KEY: dim_test_item.CREATE_HOUR_KEY.clone(),
            CREATE_DAY_KEY: dim_test_item.CREATE_DAY_KEY.clone(),
            CREATE_TIME: dim_test_item.CREATE_TIME.into_utc(),
            CREATE_USER: dim_test_item.CREATE_USER.clone(),
            UPLOAD_TIME: dim_test_item.UPLOAD_TIME.into_utc(),
            VERSION: dim_test_item.VERSION,
            IS_DELETE: dim_test_item.IS_DELETE,
        }
    }
}
