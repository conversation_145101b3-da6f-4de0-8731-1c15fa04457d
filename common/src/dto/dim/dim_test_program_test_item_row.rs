use crate::dto::dim::DimTestProgramTestItem;
use crate::utils::date::IntoDateTimeUtc;
use crate::utils::decimal::{Decimal38_18, IntoDecimal38_18};
use chrono::{DateTime, Utc};
use clickhouse::Row;
use serde::{Deserialize, Serialize};
use std::sync::Arc;

/// DIM TestProgramTestItem dimension table structure
/// Contains test program and test item dimension data
/// Corresponds to dim_test_program_test_item_local table
#[derive(Debug, Clone, Serialize, Deserialize, Row, PartialEq)]
#[allow(non_snake_case)]
pub struct DimTestProgramTestItemRow {
    // Customer and upload information
    pub CUSTOMER: Arc<str>,
    pub SUB_CUSTOMER: Arc<str>,
    pub UPLOAD_TYPE: Arc<str>,
    pub FILE_ID: u32,

    // Factory and location information
    pub FACTORY: Arc<str>,
    pub FACTORY_SITE: Arc<str>,
    pub FAB: Arc<str>,
    pub FAB_SITE: Arc<str>,

    // Test stage information
    pub TEST_AREA: Arc<str>,
    pub TEST_STAGE: Arc<str>,
    pub DEVICE_ID: Arc<str>,

    // Test program information
    pub TEST_PROGRAM: Arc<str>,
    pub TEST_PROGRAM_VERSION: Arc<str>,
    pub TEST_TEMPERATURE: Arc<str>,

    // Test item information
    pub TEST_NUM: Option<u32>,
    pub TEST_NUM_KEY: Arc<str>,
    pub TEST_TXT: Arc<str>,
    pub TEST_ITEM: Arc<str>,
    pub TESTITEM_TYPE: Arc<str>,

    // Original limits
    pub ORIGIN_HI_LIMIT: Option<Decimal38_18>,
    pub ORIGIN_HI_LIMIT_KEY: Arc<str>,
    pub ORIGIN_LO_LIMIT: Option<Decimal38_18>,
    pub ORIGIN_LO_LIMIT_KEY: Arc<str>,
    pub ORIGIN_UNITS: Arc<str>,

    // Processed limits
    pub LO_LIMIT: Option<Decimal38_18>,
    pub LO_LIMIT_KEY: Arc<str>,
    pub HI_LIMIT: Option<Decimal38_18>,
    pub HI_LIMIT_KEY: Arc<str>,
    pub UNITS: Arc<str>,

    // Test conditions
    pub CONDITION_SET: Vec<(Arc<str>, Arc<str>)>,
    pub CONDITION_SET_STR: Arc<str>,

    // System fields
    pub CREATE_HOUR_KEY: Arc<str>,
    pub CREATE_DAY_KEY: Arc<str>,
    #[serde(with = "clickhouse::serde::chrono::datetime")]
    pub CREATE_TIME: DateTime<Utc>,
    pub CREATE_USER: Arc<str>,
    #[serde(with = "clickhouse::serde::chrono::datetime")]
    pub UPLOAD_TIME: DateTime<Utc>,
    pub VERSION: u32,
    pub IS_DELETE: u8,
}

impl DimTestProgramTestItemRow {
    /// Convert DimTestProgramTestItem to DimTestProgramTestItemRow
    /// This implements the rich domain model pattern where conversion logic is in the Row model
    pub fn from_hdfs_entity(item: &DimTestProgramTestItem) -> Self {
        Self {
            CUSTOMER: item.CUSTOMER.clone(),
            SUB_CUSTOMER: item.SUB_CUSTOMER.clone(),
            UPLOAD_TYPE: item.UPLOAD_TYPE.clone(),
            FILE_ID: item.FILE_ID,
            FACTORY: item.FACTORY.clone(),
            FACTORY_SITE: item.FACTORY_SITE.clone(),
            FAB: item.FAB.clone(),
            FAB_SITE: item.FAB_SITE.clone(),
            TEST_AREA: item.TEST_AREA.clone(),
            TEST_STAGE: item.TEST_STAGE.clone(),
            DEVICE_ID: item.DEVICE_ID.clone(),
            TEST_PROGRAM: item.TEST_PROGRAM.clone(),
            TEST_PROGRAM_VERSION: item.TEST_PROGRAM_VERSION.clone(),
            TEST_TEMPERATURE: item.TEST_TEMPERATURE.clone(),
            TEST_NUM: item.TEST_NUM,
            TEST_NUM_KEY: item.TEST_NUM_KEY.clone(),
            TEST_TXT: item.TEST_TXT.clone(),
            TEST_ITEM: item.TEST_ITEM.clone(),
            TESTITEM_TYPE: item.TESTITEM_TYPE.clone(),
            ORIGIN_HI_LIMIT: item.ORIGIN_HI_LIMIT.map(|v| v.into_decimal38_18()),
            ORIGIN_HI_LIMIT_KEY: item.ORIGIN_HI_LIMIT_KEY.clone(),
            ORIGIN_LO_LIMIT: item.ORIGIN_LO_LIMIT.map(|v| v.into_decimal38_18()),
            ORIGIN_LO_LIMIT_KEY: item.ORIGIN_LO_LIMIT_KEY.clone(),
            ORIGIN_UNITS: item.ORIGIN_UNITS.clone(),
            LO_LIMIT: item.LO_LIMIT.map(|v| v.into_decimal38_18()),
            LO_LIMIT_KEY: item.LO_LIMIT_KEY.clone(),
            HI_LIMIT: item.HI_LIMIT.map(|v| v.into_decimal38_18()),
            HI_LIMIT_KEY: item.HI_LIMIT_KEY.clone(),
            UNITS: item.UNITS.clone(),
            CONDITION_SET: item.CONDITION_SET.clone(),
            CONDITION_SET_STR: item.CONDITION_SET_STR.clone(),
            CREATE_HOUR_KEY: item.CREATE_HOUR_KEY.clone(),
            CREATE_DAY_KEY: item.CREATE_DAY_KEY.clone(),
            CREATE_TIME: item.CREATE_TIME.into_utc(),
            CREATE_USER: item.CREATE_USER.clone(),
            UPLOAD_TIME: item.UPLOAD_TIME.into_utc(),
            VERSION: item.VERSION,
            IS_DELETE: item.IS_DELETE,
        }
    }
}
