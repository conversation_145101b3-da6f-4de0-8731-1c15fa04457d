use crate::dto::dwd::file_detail::FileDetail;
use crate::dto::dwd::sub_test_item_detail::SubTestItemDetail;
use crate::dwd::util::dwd_common_util::DwdCommonUtil;
use crate::model::constant::{EMPTY, SYSTEM};
use crate::parquet::RecordBatchWrapper;
use arrow::array::RecordBatch;
use arrow::datatypes::{Field, FieldRef};
use chrono::Utc;
use serde::{Deserialize, Serialize};
use serde_arrow::{schema::SchemaLike, schema::TracingOptions};
use std::sync::Arc;

#[derive(Debug, Clone, Serialize, Deserialize)]
#[allow(non_snake_case)]
pub struct DimTestItem {
    pub CUSTOMER: Arc<str>,
    pub SUB_CUSTOMER: Arc<str>,
    pub UPLOAD_TYPE: Arc<str>,
    pub FILE_ID: u32,
    pub FILE_NAME: Arc<str>,
    pub FACTORY: Arc<str>,
    pub FACTORY_SITE: Arc<str>,
    pub FAB: Arc<str>,
    pub FAB_SITE: Arc<str>,
    pub TEST_AREA: Arc<str>,
    pub TEST_STAGE: Arc<str>,
    pub LOT_TYPE: Arc<str>,
    pub DEVICE_ID: Arc<str>,
    pub LOT_ID: Arc<str>,
    pub SBLOT_ID: Arc<str>,
    pub PROCESS: Arc<str>,
    pub WAFER_ID: Arc<str>,
    pub WAFER_ID_KEY: Arc<str>,
    pub WAFER_NO: Arc<str>,
    pub WAFER_NO_KEY: Arc<str>,
    pub WAFER_LOT_ID: Arc<str>,
    pub FABWF_ID: Arc<str>,
    pub TEST_PROGRAM: Arc<str>,
    pub TEST_PROGRAM_VERSION: Arc<str>,
    pub TEST_TEMPERATURE: Arc<str>,
    pub TESTER_NAME: Arc<str>,
    pub TESTER_TYPE: Arc<str>,
    pub OPERATOR_NAME: Arc<str>,
    pub PROBER_HANDLER_TYP: Arc<str>,
    pub PROBER_HANDLER_ID: Arc<str>,
    pub PROBECARD_LOADBOARD_TYP: Arc<str>,
    pub PROBECARD_LOADBOARD_ID: Arc<str>,
    pub START_TIME: Option<i64>, // Timestamp in milliseconds
    pub END_TIME: Option<i64>,   // Timestamp in milliseconds
    pub START_HOUR_KEY: Arc<str>,
    pub START_DAY_KEY: Arc<str>,
    pub END_HOUR_KEY: Arc<str>,
    pub END_DAY_KEY: Arc<str>,
    pub TEST_HEAD: Option<u32>,
    pub SITE: Option<u32>,
    pub SITE_KEY: Arc<str>,
    pub HBIN_NUM: Option<u32>,
    pub HBIN_NUM_KEY: Arc<str>,
    pub SBIN_NUM: Option<u32>,
    pub SBIN_NUM_KEY: Arc<str>,
    pub SBIN_PF: Arc<str>,
    pub SBIN_NAM: Arc<str>,
    pub HBIN_PF: Arc<str>,
    pub HBIN_NAM: Arc<str>,
    pub HBIN: Arc<str>,
    pub SBIN: Arc<str>,
    pub TEST_NUM: Option<u32>,
    pub TEST_TXT: Arc<str>,
    pub TEST_ITEM: Arc<str>,
    pub TESTITEM_TYPE: Arc<str>,
    pub ORIGIN_HI_LIMIT: Option<f64>,
    pub ORIGIN_LO_LIMIT: Option<f64>,
    pub ORIGIN_UNITS: Arc<str>,
    pub LO_LIMIT: Option<f64>,
    pub HI_LIMIT: Option<f64>,
    pub UNITS: Arc<str>,
    pub CONDITION_SET: Vec<(Arc<str>, Arc<str>)>,
    pub CREATE_HOUR_KEY: Arc<str>,
    pub CREATE_DAY_KEY: Arc<str>,
    pub CREATE_TIME: i64, // Timestamp in milliseconds
    pub CREATE_USER: Arc<str>,
    pub UPLOAD_TIME: i64, // Timestamp in milliseconds
    pub VERSION: i64,
    pub IS_DELETE: u8,
}

impl DimTestItem {
    /// Build DimTestItem from SubTestItemDetail and FileDetail
    /// Corresponds to buildTestItem method in TestItemCommonService.scala
    pub fn build_test_item(sub_test_item_detail: &SubTestItemDetail, file_detail: &FileDetail, is_cp: bool) -> Self {
        // Helper function to convert Option<String> to Arc<str>
        let str_arc =
            |val: &Option<String>| -> Arc<str> { Arc::from(val.as_ref().map(|s| s.as_str()).unwrap_or(EMPTY)) };

        // Calculate wafer_id_key and wafer_no_key based on test area
        let wafer_id_key = if is_cp { str_arc(&sub_test_item_detail.WAFER_ID) } else { Arc::from(EMPTY) };

        let wafer_no_key = if is_cp { str_arc(&sub_test_item_detail.WAFER_NO) } else { Arc::from(EMPTY) };

        let sblot_id = if is_cp { Arc::from(EMPTY) } else { Arc::from(file_detail.SBLOT_ID.as_str()) };

        let now = Utc::now();

        Self {
            // fileDetail.CUSTOMER
            CUSTOMER: Arc::from(file_detail.CUSTOMER.as_str()),
            // fileDetail.SUB_CUSTOMER
            SUB_CUSTOMER: Arc::from(file_detail.SUB_CUSTOMER.as_str()),
            // fileDetail.UPLOAD_TYPE
            UPLOAD_TYPE: Arc::from(file_detail.UPLOAD_TYPE.as_str()),
            // 0
            FILE_ID: 0, // Set to 0 as in Scala version
            // EMPTY
            FILE_NAME: Arc::from(EMPTY), // Set to EMPTY as in Scala version
            // fileDetail.FACTORY
            FACTORY: Arc::from(file_detail.FACTORY.as_str()),
            // fileDetail.FACTORY_SITE
            FACTORY_SITE: Arc::from(file_detail.FACTORY_SITE.as_str()),
            // fileDetail.FAB
            FAB: Arc::from(file_detail.FAB.as_str()),
            // fileDetail.FAB_SITE
            FAB_SITE: Arc::from(file_detail.FAB_SITE.as_str()),
            // fileDetail.TEST_AREA
            TEST_AREA: Arc::from(file_detail.TEST_AREA.as_str()),
            // fileDetail.TEST_STAGE
            TEST_STAGE: Arc::from(file_detail.TEST_STAGE.as_str()),
            // fileDetail.LOT_TYPE
            LOT_TYPE: Arc::from(file_detail.LOT_TYPE.as_str()),
            // fileDetail.DEVICE_ID
            DEVICE_ID: Arc::from(file_detail.DEVICE_ID.as_str()),
            // fileDetail.LOT_ID
            LOT_ID: Arc::from(file_detail.LOT_ID.as_str()),
            // if (SUPPORT_CP_TEST_AREA_LIST.contains(TestArea.of(fileDetail.TEST_AREA))) EMPTY else fileDetail.SBLOT_ID
            SBLOT_ID: sblot_id,
            // fileDetail.PROCESS
            PROCESS: str_arc(&file_detail.PROCESS),
            // testItemDetail.WAFER_ID
            WAFER_ID: str_arc(&sub_test_item_detail.WAFER_ID),
            // if (SUPPORT_CP_TEST_AREA_LIST.contains(TestArea.of(fileDetail.TEST_AREA))) testItemDetail.WAFER_ID else EMPTY
            WAFER_ID_KEY: wafer_id_key,
            // testItemDetail.WAFER_NO
            WAFER_NO: str_arc(&sub_test_item_detail.WAFER_NO),
            // if (SUPPORT_CP_TEST_AREA_LIST.contains(TestArea.of(fileDetail.TEST_AREA))) testItemDetail.WAFER_NO else EMPTY
            WAFER_NO_KEY: wafer_no_key,
            // testItemDetail.WAFER_LOT_ID
            WAFER_LOT_ID: str_arc(&sub_test_item_detail.WAFER_LOT_ID),
            // fileDetail.FABWF_ID
            FABWF_ID: Arc::from(file_detail.FABWF_ID.as_str()),
            // fileDetail.TEST_PROGRAM
            TEST_PROGRAM: Arc::from(file_detail.TEST_PROGRAM.as_str()),
            // fileDetail.TEST_PROGRAM_VERSION
            TEST_PROGRAM_VERSION: Arc::from(file_detail.TEST_PROGRAM_VERSION.as_str()),
            // fileDetail.TEST_TEMPERATURE
            TEST_TEMPERATURE: Arc::from(file_detail.TEST_TEMPERATURE.as_str()),
            // fileDetail.TESTER_NAME
            TESTER_NAME: Arc::from(file_detail.TESTER_NAME.as_str()),
            // fileDetail.TESTER_TYPE
            TESTER_TYPE: Arc::from(file_detail.TESTER_TYPE.as_str()),
            // fileDetail.OPERATOR_NAME
            OPERATOR_NAME: Arc::from(file_detail.OPERATOR_NAME.as_str()),
            // fileDetail.PROBER_HANDLER_TYP
            PROBER_HANDLER_TYP: Arc::from(file_detail.PROBER_HANDLER_TYP.as_str()),
            // fileDetail.PROBER_HANDLER_ID
            PROBER_HANDLER_ID: Arc::from(file_detail.PROBER_HANDLER_ID.as_str()),
            // fileDetail.PROBECARD_LOADBOARD_TYP
            PROBECARD_LOADBOARD_TYP: Arc::from(file_detail.PROBECARD_LOADBOARD_TYP.as_str()),
            // fileDetail.PROBECARD_LOADBOARD_ID
            PROBECARD_LOADBOARD_ID: Arc::from(file_detail.PROBECARD_LOADBOARD_ID.as_str()),
            // fileDetail.START_TIME
            START_TIME: file_detail.START_TIME.map(|dt| dt.timestamp_millis()),
            // fileDetail.END_TIME
            END_TIME: file_detail.END_TIME.map(|dt| dt.timestamp_millis()),
            // fileDetail.START_HOUR_KEY
            START_HOUR_KEY: Arc::from(file_detail.START_HOUR_KEY.as_str()),
            // fileDetail.START_DAY_KEY
            START_DAY_KEY: Arc::from(file_detail.START_DAY_KEY.as_str()),
            // fileDetail.END_HOUR_KEY
            END_HOUR_KEY: Arc::from(file_detail.END_HOUR_KEY.as_str()),
            // fileDetail.END_DAY_KEY
            END_DAY_KEY: Arc::from(file_detail.END_DAY_KEY.as_str()),
            // testItemDetail.TEST_HEAD
            TEST_HEAD: sub_test_item_detail.TEST_HEAD.map(|v| v as u32),
            // testItemDetail.SITE
            SITE: sub_test_item_detail.SITE.map(|v| v as u32),
            // if (testItemDetail.SITE == null) EMPTY else testItemDetail.SITE.toString
            SITE_KEY: sub_test_item_detail.SITE.map_or_else(|| Arc::from(EMPTY), |v| v.to_string().into()),
            // testItemDetail.HBIN_NUM
            HBIN_NUM: sub_test_item_detail.HBIN_NUM.map(|v| v as u32),
            // if (testItemDetail.HBIN_NUM == null) EMPTY else testItemDetail.HBIN_NUM.toString
            HBIN_NUM_KEY: sub_test_item_detail
                .HBIN_NUM
                .map_or_else(|| Arc::from(EMPTY), |v| v.to_string().into()),
            // testItemDetail.SBIN_NUM
            SBIN_NUM: sub_test_item_detail.SBIN_NUM.map(|v| v as u32),
            // if (testItemDetail.SBIN_NUM == null) EMPTY else testItemDetail.SBIN_NUM.toString
            SBIN_NUM_KEY: sub_test_item_detail
                .SBIN_NUM
                .map_or_else(|| Arc::from(EMPTY), |v| v.to_string().into()),
            // testItemDetail.SBIN_PF
            SBIN_PF: str_arc(&sub_test_item_detail.SBIN_PF),
            // testItemDetail.SBIN_NAM
            SBIN_NAM: str_arc(&sub_test_item_detail.SBIN_NAM),
            // testItemDetail.HBIN_PF
            HBIN_PF: str_arc(&sub_test_item_detail.HBIN_PF),
            // testItemDetail.HBIN_NAM
            HBIN_NAM: str_arc(&sub_test_item_detail.HBIN_NAM),
            // testItemDetail.HBIN
            HBIN: str_arc(&sub_test_item_detail.HBIN),
            // testItemDetail.SBIN
            SBIN: str_arc(&sub_test_item_detail.SBIN),
            // testItemDetail.TEST_NUM
            TEST_NUM: sub_test_item_detail.TEST_NUM.map(|v| v as u32),
            // testItemDetail.TEST_TXT
            TEST_TXT: str_arc(&sub_test_item_detail.TEST_TXT),
            // testItemDetail.TEST_ITEM
            TEST_ITEM: str_arc(&sub_test_item_detail.TEST_ITEM),
            // testItemDetail.TESTITEM_TYPE
            TESTITEM_TYPE: str_arc(&sub_test_item_detail.TESTITEM_TYPE),
            // testItemDetail.ORIGIN_HI_LIMIT
            ORIGIN_HI_LIMIT: sub_test_item_detail.ORIGIN_HI_LIMIT,
            // testItemDetail.ORIGIN_LO_LIMIT
            ORIGIN_LO_LIMIT: sub_test_item_detail.ORIGIN_LO_LIMIT,
            // testItemDetail.ORIGIN_UNITS
            ORIGIN_UNITS: str_arc(&sub_test_item_detail.ORIGIN_UNITS),
            // testItemDetail.LO_LIMIT
            LO_LIMIT: sub_test_item_detail.LO_LIMIT,
            // testItemDetail.HI_LIMIT
            HI_LIMIT: sub_test_item_detail.HI_LIMIT,
            // testItemDetail.UNITS
            UNITS: str_arc(&sub_test_item_detail.UNITS),
            // testItemDetail.CONDITION_SET
            CONDITION_SET: sub_test_item_detail
                .CONDITION_SET
                .as_ref()
                .map(|cs| cs.iter().map(|(k, v)| (Arc::from(k.as_str()), Arc::from(v.as_str()))).collect())
                .unwrap_or_default(),
            // null
            CREATE_HOUR_KEY: Arc::from(EMPTY),
            // null
            CREATE_DAY_KEY: Arc::from(EMPTY),
            // null
            CREATE_TIME: now.timestamp_millis(),
            // SYSTEM
            CREATE_USER: Arc::from(SYSTEM),
            // fileDetail.UPLOAD_TIME
            UPLOAD_TIME: file_detail.UPLOAD_TIME.unwrap_or(now).timestamp_millis(),
            // 0
            VERSION: 0, // Set to 0 initially as in Scala version
            // 0
            IS_DELETE: 0,
        }
    }
}

impl RecordBatchWrapper for DimTestItem {
    fn from_record_batch(batch: &RecordBatch) -> Result<Vec<Self>, String>
    where
        Self: Sized,
    {
        let result: Vec<DimTestItem> = serde_arrow::from_record_batch(batch)
            .map_err(|e| format!("Failed to deserialize from RecordBatch: {}", e))?;
        Ok(result)
    }

    fn to_record_batch(data: &[Self]) -> Result<RecordBatch, String>
    where
        Self: Sized,
    {
        if data.is_empty() {
            return Err("Cannot create RecordBatch from empty data".to_string());
        }

        // Use DIM layer field processing logic
        let fields: Vec<Arc<Field>> = Vec::<FieldRef>::from_type::<DimTestItem>(
            TracingOptions::default()
                .allow_null_fields(true)
                .map_as_struct(false)
                .strings_as_large_utf8(false),
        )
        .map_err(|e| format!("Failed to create fields from type: {}", e))?;

        let record_batch =
            serde_arrow::to_record_batch(&fields, &data).map_err(|e| format!("Failed to create RecordBatch: {}", e))?;

        Ok(record_batch)
    }
}

impl Default for DimTestItem {
    fn default() -> Self {
        use crate::model::constant::EMPTY;

        Self {
            CUSTOMER: Arc::from(EMPTY),
            SUB_CUSTOMER: Arc::from(EMPTY),
            UPLOAD_TYPE: Arc::from(EMPTY),
            FILE_ID: 0,
            FILE_NAME: Arc::from(EMPTY),
            FACTORY: Arc::from(EMPTY),
            FACTORY_SITE: Arc::from(EMPTY),
            FAB: Arc::from(EMPTY),
            FAB_SITE: Arc::from(EMPTY),
            TEST_AREA: Arc::from(EMPTY),
            TEST_STAGE: Arc::from(EMPTY),
            LOT_TYPE: Arc::from(EMPTY),
            DEVICE_ID: Arc::from(EMPTY),
            LOT_ID: Arc::from(EMPTY),
            SBLOT_ID: Arc::from(EMPTY),
            PROCESS: Arc::from(EMPTY),
            WAFER_ID: Arc::from(EMPTY),
            WAFER_ID_KEY: Arc::from(EMPTY),
            WAFER_NO: Arc::from(EMPTY),
            WAFER_NO_KEY: Arc::from(EMPTY),
            WAFER_LOT_ID: Arc::from(EMPTY),
            FABWF_ID: Arc::from(EMPTY),
            TEST_PROGRAM: Arc::from(EMPTY),
            TEST_PROGRAM_VERSION: Arc::from(EMPTY),
            TEST_TEMPERATURE: Arc::from(EMPTY),
            TESTER_NAME: Arc::from(EMPTY),
            TESTER_TYPE: Arc::from(EMPTY),
            OPERATOR_NAME: Arc::from(EMPTY),
            PROBER_HANDLER_TYP: Arc::from(EMPTY),
            PROBER_HANDLER_ID: Arc::from(EMPTY),
            PROBECARD_LOADBOARD_TYP: Arc::from(EMPTY),
            PROBECARD_LOADBOARD_ID: Arc::from(EMPTY),
            START_TIME: None,
            END_TIME: None,
            START_HOUR_KEY: Arc::from(EMPTY),
            START_DAY_KEY: Arc::from(EMPTY),
            END_HOUR_KEY: Arc::from(EMPTY),
            END_DAY_KEY: Arc::from(EMPTY),
            TEST_HEAD: None,
            SITE: None,
            SITE_KEY: Arc::from(EMPTY),
            HBIN_NUM: None,
            HBIN_NUM_KEY: Arc::from(EMPTY),
            SBIN_NUM: None,
            SBIN_NUM_KEY: Arc::from(EMPTY),
            SBIN_PF: Arc::from(EMPTY),
            SBIN_NAM: Arc::from(EMPTY),
            HBIN_PF: Arc::from(EMPTY),
            HBIN_NAM: Arc::from(EMPTY),
            HBIN: Arc::from(EMPTY),
            SBIN: Arc::from(EMPTY),
            TEST_NUM: None,
            TEST_TXT: Arc::from(EMPTY),
            TEST_ITEM: Arc::from(EMPTY),
            TESTITEM_TYPE: Arc::from(EMPTY),
            ORIGIN_HI_LIMIT: None,
            ORIGIN_LO_LIMIT: None,
            ORIGIN_UNITS: Arc::from(EMPTY),
            LO_LIMIT: None,
            HI_LIMIT: None,
            UNITS: Arc::from(EMPTY),
            CONDITION_SET: Vec::new(),
            CREATE_HOUR_KEY: Arc::from(EMPTY),
            CREATE_DAY_KEY: Arc::from(EMPTY),
            CREATE_TIME: 0,
            CREATE_USER: Arc::from(EMPTY),
            UPLOAD_TIME: 0,
            VERSION: 0,
            IS_DELETE: 0,
        }
    }
}
