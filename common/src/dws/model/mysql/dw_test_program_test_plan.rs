use sqlx::FromRow;

#[derive(Debug, Clone, FromRow)]
pub struct DwTestProgramTestPlan {
    pub id: Option<i64>,
    pub customer: Option<String>,
    pub sub_customer: Option<String>,
    pub upload_type: Option<String>,
    pub test_area: Option<String>,
    pub factory: Option<String>,
    pub factory_site: Option<String>,
    pub fab: Option<String>,
    pub fab_site: Option<String>,
    pub device_id: Option<String>,
    pub test_stage: Option<String>,
    pub lot_type: Option<String>,
    pub test_program: Option<String>,
    pub test_item: Option<String>,
    pub test_order: Option<i64>,
    pub testitem_type: Option<String>,
    pub test_num: Option<i64>,
    pub test_txt: Option<String>,
    pub bin_relation: Option<String>,
    pub hbins: Option<String>,
    pub sbins: Option<String>,
    pub unit_scale: Option<f64>,
    pub custom_unit: Option<String>,
    pub test_order_manual_import_flag: Option<i32>,
    pub bin_relation_manual_import_flag: Option<i32>,
    pub create_time: Option<chrono::DateTime<chrono::Utc>>,
    pub update_time: Option<chrono::DateTime<chrono::Utc>>,
    pub create_user: Option<String>,
    pub update_user: Option<String>,
}