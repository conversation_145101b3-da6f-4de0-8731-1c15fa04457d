//! DWS服务基础实现
//!
//! 对应Scala中的DwsService类

use regex::Regex;

use crate::dto::dwd::die_detail_row::DieDetailRow;
use crate::dto::dwd::die_detail_parquet::DieDetailParquet;
use crate::dto::dwd::sub_test_item_detail::SubTestItemDetail;
use crate::dws::model::sblot_aggregation::Sblot;
use crate::model::constant::ALL;
use std::collections::HashMap;
use std::sync::Arc;

/// DWS服务基础类
/// 对应Scala中的com.guwave.onedata.dataware.dw.common.dws.service.DwsService
pub struct DwsService;

impl DwsService {
    /// 构建DWS子Die详情 - 从DieDetailParquet
    /// 对应Scala中的buildDwsSubDieDetail方法，处理parquet格式的数据
    pub fn build_dws_sub_die_detail_from_parquet(die_detail: &DieDetailParquet) -> DwsSubDieDetail {
        DwsSubDieDetail {
            CUSTOMER: Arc::from(die_detail.CUSTOMER.as_deref().unwrap_or("")),
            SUB_CUSTOMER: Arc::from(die_detail.SUB_CUSTOMER.as_deref().unwrap_or("")),
            UPLOAD_TYPE: Arc::from(die_detail.UPLOAD_TYPE.as_deref().unwrap_or("")),
            FILE_ID: die_detail.FILE_ID,
            FILE_NAME: Arc::from(die_detail.FILE_NAME.as_deref().unwrap_or("")),
            FILE_TYPE: Arc::from(die_detail.FILE_TYPE.as_deref().unwrap_or("")),
            FACTORY: Arc::from(die_detail.FACTORY.as_deref().unwrap_or("")),
            FACTORY_SITE: Arc::from(die_detail.FACTORY_SITE.as_deref().unwrap_or("")),
            FAB: Arc::from(die_detail.FAB.as_deref().unwrap_or("")),
            FAB_SITE: Arc::from(die_detail.FAB_SITE.as_deref().unwrap_or("")),
            LOT_TYPE: Arc::from(die_detail.LOT_TYPE.as_deref().unwrap_or("")),
            TEST_AREA: Arc::from(die_detail.TEST_AREA.as_deref().unwrap_or("")),
            ECID: Arc::from(die_detail.ECID.as_deref().unwrap_or("")),
            IS_STANDARD_ECID: die_detail.IS_STANDARD_ECID,
            SITE: die_detail.SITE,
            OFFLINE_RETEST: die_detail.OFFLINE_RETEST,
            OFFLINE_RETEST_IGNORE_TP: die_detail.OFFLINE_RETEST_IGNORE_TP,
            ONLINE_RETEST: die_detail.ONLINE_RETEST,
            INTERRUPT: die_detail.INTERRUPT,
            INTERRUPT_IGNORE_TP: die_detail.INTERRUPT_IGNORE_TP,
            DUP_RETEST: die_detail.DUP_RETEST,
            DUP_RETEST_IGNORE_TP: die_detail.DUP_RETEST_IGNORE_TP,
            BATCH_NUM: die_detail.BATCH_NUM,
            BATCH_NUM_IGNORE_TP: die_detail.BATCH_NUM_IGNORE_TP,
            IS_FIRST_TEST: die_detail.IS_FIRST_TEST,
            IS_FINAL_TEST: die_detail.IS_FINAL_TEST,
            IS_FIRST_TEST_IGNORE_TP: die_detail.IS_FIRST_TEST_IGNORE_TP,
            IS_FINAL_TEST_IGNORE_TP: die_detail.IS_FINAL_TEST_IGNORE_TP,
            IS_DUP_FIRST_TEST: die_detail.IS_DUP_FIRST_TEST,
            IS_DUP_FINAL_TEST: die_detail.IS_DUP_FINAL_TEST,
            IS_DUP_FIRST_TEST_IGNORE_TP: die_detail.IS_DUP_FIRST_TEST_IGNORE_TP,
            IS_DUP_FINAL_TEST_IGNORE_TP: die_detail.IS_DUP_FINAL_TEST_IGNORE_TP,
            WAFER_ID: Arc::from(die_detail.WAFER_ID.as_deref().unwrap_or("")),
            WAFER_NO: Arc::from(die_detail.WAFER_NO.as_deref().unwrap_or("")),
            LOT_ID: Arc::from(die_detail.LOT_ID.as_deref().unwrap_or("")),
            SBLOT_ID: Arc::from(die_detail.SBLOT_ID.as_deref().unwrap_or("")),
            WAFER_LOT_ID: Arc::from(die_detail.WAFER_LOT_ID.as_deref().unwrap_or("")),
            PROBER_HANDLER_ID: Arc::from(die_detail.PROBER_HANDLER_ID.as_deref().unwrap_or("")),
            TESTER_TYPE: Arc::from(die_detail.TESTER_TYPE.as_deref().unwrap_or("")),
            OPERATOR_NAME: Arc::from(die_detail.OPERATOR_NAME.as_deref().unwrap_or("")),
            TEST_STAGE: Arc::from(die_detail.TEST_STAGE.as_deref().unwrap_or("")),
            DEVICE_ID: Arc::from(die_detail.DEVICE_ID.as_deref().unwrap_or("")),
            TEST_PROGRAM: Arc::from(die_detail.TEST_PROGRAM.as_deref().unwrap_or("")),
            TEST_TEMPERATURE: Arc::from(die_detail.TEST_TEMPERATURE.as_deref().unwrap_or("")),
            TEST_PROGRAM_VERSION: Arc::from(die_detail.TEST_PROGRAM_VERSION.as_deref().unwrap_or("")),
            TESTER_NAME: Arc::from(die_detail.TESTER_NAME.as_deref().unwrap_or("")),
            PROBECARD_LOADBOARD_ID: Arc::from(die_detail.PROBECARD_LOADBOARD_ID.as_deref().unwrap_or("")),
            FLOW_ID: Arc::from(die_detail.FLOW_ID.as_deref().unwrap_or("")),
            FLOW_ID_IGNORE_TP: Arc::from(die_detail.FLOW_ID_IGNORE_TP.as_deref().unwrap_or("")),
            DIE_CNT: die_detail.DIE_CNT,
            X_COORD: die_detail.X_COORD,
            Y_COORD: die_detail.Y_COORD,
            DIE_X: die_detail.DIE_X,
            DIE_Y: die_detail.DIE_Y,
            WF_FLAT: Arc::from(die_detail.WF_FLAT.as_deref().unwrap_or("")),
            FABWF_ID: Arc::from(die_detail.FABWF_ID.as_deref().unwrap_or("")),
            POS_X: Arc::from(die_detail.POS_X.as_deref().unwrap_or("")),
            POS_Y: Arc::from(die_detail.POS_Y.as_deref().unwrap_or("")),
            RETICLE_X: die_detail.RETICLE_X,
            RETICLE_Y: die_detail.RETICLE_Y,
            SITE_ID: Arc::from(die_detail.SITE_ID.as_deref().unwrap_or("")),
            TEST_TIME: die_detail.TEST_TIME,
            PART_ID: Arc::from(die_detail.PART_ID.as_deref().unwrap_or("")),
            SBIN_NUM: die_detail.SBIN_NUM,
            SBIN_PF: Arc::from(die_detail.SBIN_PF.as_deref().unwrap_or("")),
            SBIN_NAM: Arc::from(die_detail.SBIN_NAM.as_deref().unwrap_or("")),
            HBIN_NUM: die_detail.HBIN_NUM,
            HBIN_PF: Arc::from(die_detail.HBIN_PF.as_deref().unwrap_or("")),
            HBIN_NAM: Arc::from(die_detail.HBIN_NAM.as_deref().unwrap_or("")),
            HBIN: Arc::from(die_detail.HBIN.as_deref().unwrap_or("")),
            SBIN: Arc::from(die_detail.SBIN.as_deref().unwrap_or("")),
            START_TIME: die_detail.START_TIME,
            END_TIME: die_detail.END_TIME,
            START_HOUR_KEY: Arc::from(die_detail.START_HOUR_KEY.as_deref().unwrap_or("")),
            START_DAY_KEY: Arc::from(die_detail.START_DAY_KEY.as_deref().unwrap_or("")),
            END_HOUR_KEY: Arc::from(die_detail.END_HOUR_KEY.as_deref().unwrap_or("")),
            END_DAY_KEY: Arc::from(die_detail.END_DAY_KEY.as_deref().unwrap_or("")),
            C_PART_ID: die_detail.C_PART_ID,
            RETEST_BIN_NUM: Arc::from(die_detail.RETEST_BIN_NUM.as_deref().unwrap_or("")),
            PROCESS: Arc::from(die_detail.PROCESS.as_deref().unwrap_or("")),
            CREATE_TIME: die_detail.CREATE_TIME,
            UPLOAD_TIME: die_detail.UPLOAD_TIME,
            TEST_HEAD: die_detail.TEST_HEAD,
            PROBER_HANDLER_TYP: Arc::from(die_detail.PROBER_HANDLER_TYP.as_deref().unwrap_or("")),
            PROBECARD_LOADBOARD_TYP: Arc::from(die_detail.PROBECARD_LOADBOARD_TYP.as_deref().unwrap_or("")),
        }
    }

    /// 构建DWS子测试项详情并填充文件信息
    /// 整合了buildDwsSubTestItemDetail和fillFileInfo的功能
    pub fn build_dws_sub_test_item_detail_with_file_info(
        test_item_detail: &SubTestItemDetail,
        file_detail_map: &std::collections::HashMap<i64, DwsSubFileDetail>,
    ) -> DwsSubTestItemDetail {
        let mut dws_item = Self::build_dws_sub_test_item_detail(test_item_detail);
        
        // 填充文件信息
        if let Some(file_id) = test_item_detail.FILE_ID {
            if let Some(file_detail) = file_detail_map.get(&file_id) {
                Self::fill_file_info(&mut dws_item, file_detail);
            }
        }
        
        dws_item
    }

    /// 构建DWS子测试项详情
    /// 对应Scala中的buildDwsSubTestItemDetail方法
    pub fn build_dws_sub_test_item_detail(test_item_detail: &SubTestItemDetail) -> DwsSubTestItemDetail {
        DwsSubTestItemDetail {
            CUSTOMER: Arc::from(""), // 将在fill_file_info中填充
            SUB_CUSTOMER: Arc::from(""),
            UPLOAD_TYPE: Arc::from(""),
            FILE_ID: test_item_detail.FILE_ID,
            FILE_NAME: Arc::from(""),
            FILE_TYPE: Arc::from(""),
            FACTORY: Arc::from(""),
            FACTORY_SITE: Arc::from(""),
            FAB: Arc::from(""),
            FAB_SITE: Arc::from(""),
            LOT_TYPE: Arc::from(""),
            TEST_AREA: Arc::from(""),
            ECID: Arc::from(test_item_detail.ECID.as_deref().unwrap_or("")),
            OFFLINE_RETEST: None,
            ONLINE_RETEST: test_item_detail.ONLINE_RETEST,
            INTERRUPT: None,
            DUP_RETEST: None,
            BATCH_NUM: None,
            IS_FIRST_TEST: test_item_detail.IS_FIRST_TEST,
            IS_FINAL_TEST: test_item_detail.IS_FINAL_TEST,
            IS_FIRST_TEST_IGNORE_TP: test_item_detail.IS_FIRST_TEST_IGNORE_TP,
            IS_FINAL_TEST_IGNORE_TP: test_item_detail.IS_FINAL_TEST_IGNORE_TP,
            IS_DUP_FIRST_TEST: test_item_detail.IS_DUP_FIRST_TEST,
            IS_DUP_FINAL_TEST: test_item_detail.IS_DUP_FINAL_TEST,
            IS_DUP_FIRST_TEST_IGNORE_TP: test_item_detail.IS_DUP_FIRST_TEST_IGNORE_TP,
            IS_DUP_FINAL_TEST_IGNORE_TP: test_item_detail.IS_DUP_FINAL_TEST_IGNORE_TP,
            WAFER_ID: Arc::from(test_item_detail.WAFER_ID.as_deref().unwrap_or("")),
            WAFER_NO: Arc::from(test_item_detail.WAFER_NO.as_deref().unwrap_or("")),
            LOT_ID: Arc::from(""),
            SBLOT_ID: Arc::from(""),
            WAFER_LOT_ID: Arc::from(test_item_detail.WAFER_LOT_ID.as_deref().unwrap_or("")),
            PROBER_HANDLER_ID: Arc::from(""),
            TESTER_TYPE: Arc::from(""),
            TEST_STAGE: Arc::from(""),
            DEVICE_ID: Arc::from(""),
            TEST_PROGRAM: Arc::from(""),
            TEST_TEMPERATURE: Arc::from(""),
            TEST_PROGRAM_VERSION: Arc::from(""),
            TESTER_NAME: Arc::from(""),
            PROBECARD_LOADBOARD_ID: Arc::from(""),
            SITE: test_item_detail.SITE,
            X_COORD: test_item_detail.X_COORD,
            Y_COORD: test_item_detail.Y_COORD,
            PART_ID: Arc::from(test_item_detail.PART_ID.as_deref().unwrap_or("")),
            SBIN_NUM: test_item_detail.SBIN_NUM,
            SBIN_PF: Arc::from(test_item_detail.SBIN_PF.as_deref().unwrap_or("")),
            SBIN_NAM: Arc::from(test_item_detail.SBIN_NAM.as_deref().unwrap_or("")),
            HBIN_NUM: test_item_detail.HBIN_NUM,
            HBIN_PF: Arc::from(test_item_detail.HBIN_PF.as_deref().unwrap_or("")),
            HBIN_NAM: Arc::from(test_item_detail.HBIN_NAM.as_deref().unwrap_or("")),
            HBIN: Arc::from(test_item_detail.HBIN.as_deref().unwrap_or("")),
            SBIN: Arc::from(test_item_detail.SBIN.as_deref().unwrap_or("")),
            TEST_NUM: test_item_detail.TEST_NUM,
            TEST_TXT: Arc::from(test_item_detail.TEST_TXT.as_deref().unwrap_or("")),
            TEST_ITEM: Arc::from(test_item_detail.TEST_ITEM.as_deref().unwrap_or("")),
            TEST_ORDER: test_item_detail.TEST_ORDER,
            LO_SPEC: test_item_detail.LO_SPEC,
            HI_SPEC: test_item_detail.HI_SPEC,
            LO_LIMIT: test_item_detail.LO_LIMIT,
            HI_LIMIT: test_item_detail.HI_LIMIT,
            ORIGIN_HI_LIMIT: test_item_detail.ORIGIN_HI_LIMIT,
            ORIGIN_LO_LIMIT: test_item_detail.ORIGIN_LO_LIMIT,
            TEST_VALUE: test_item_detail.TEST_VALUE,
            TEST_RESULT: test_item_detail.TEST_RESULT,
            START_TIME: None,
            END_TIME: None,
            START_HOUR_KEY: Arc::from(""),
            START_DAY_KEY: Arc::from(""),
            END_HOUR_KEY: Arc::from(""),
            END_DAY_KEY: Arc::from(""),
            FLOW_ID: Arc::from(""),
            UNITS: Arc::from(test_item_detail.UNITS.as_deref().unwrap_or("")),
            ORIGIN_UNITS: Arc::from(test_item_detail.ORIGIN_UNITS.as_deref().unwrap_or("")),
            TESTITEM_TYPE: Arc::from(test_item_detail.TESTITEM_TYPE.as_deref().unwrap_or("")),
            C_PART_ID: test_item_detail.C_PART_ID,
            RETEST_BIN_NUM: Arc::from(""),
            PROCESS: Arc::from(""),
            CREATE_TIME: Some(test_item_detail.CREATE_TIME),
            UPLOAD_TIME: None,
        }
    }

    /// 填充文件信息
    /// 对应Scala中的fillFileInfo方法
    
    /// 填充文件信息到测试项详情 (对应Scala中的fillFileInfo方法)
    pub fn fill_file_info(test_item_detail: &mut DwsSubTestItemDetail, file_detail: &DwsSubFileDetail) {
        test_item_detail.CUSTOMER = file_detail.CUSTOMER.clone();
        test_item_detail.SUB_CUSTOMER = file_detail.SUB_CUSTOMER.clone();
        test_item_detail.UPLOAD_TYPE = file_detail.UPLOAD_TYPE.clone();
        test_item_detail.FILE_ID = file_detail.FILE_ID;
        test_item_detail.FILE_NAME = file_detail.FILE_NAME.clone();
        test_item_detail.FILE_TYPE = file_detail.FILE_TYPE.clone();
        test_item_detail.FACTORY = file_detail.FACTORY.clone();
        test_item_detail.FACTORY_SITE = file_detail.FACTORY_SITE.clone();
        test_item_detail.FAB = file_detail.FAB.clone();
        test_item_detail.FAB_SITE = file_detail.FAB_SITE.clone();
        test_item_detail.LOT_TYPE = file_detail.LOT_TYPE.clone();
        test_item_detail.TEST_AREA = file_detail.TEST_AREA.clone();
        test_item_detail.OFFLINE_RETEST = file_detail.OFFLINE_RETEST;
        test_item_detail.INTERRUPT = file_detail.INTERRUPT;
        test_item_detail.DUP_RETEST = file_detail.DUP_RETEST;
        test_item_detail.BATCH_NUM = file_detail.BATCH_NUM;
        test_item_detail.LOT_ID = file_detail.LOT_ID.clone();
        test_item_detail.SBLOT_ID = file_detail.SBLOT_ID.clone();
        test_item_detail.PROBER_HANDLER_ID = file_detail.PROBER_HANDLER_ID.clone();
        test_item_detail.TESTER_TYPE = file_detail.TESTER_TYPE.clone();
        test_item_detail.TEST_STAGE = file_detail.TEST_STAGE.clone();
        test_item_detail.DEVICE_ID = file_detail.DEVICE_ID.clone();
        test_item_detail.TEST_PROGRAM = file_detail.TEST_PROGRAM.clone();
        test_item_detail.TEST_TEMPERATURE = file_detail.TEST_TEMPERATURE.clone();
        test_item_detail.TEST_PROGRAM_VERSION = file_detail.TEST_PROGRAM_VERSION.clone();
        test_item_detail.TESTER_NAME = file_detail.TESTER_NAME.clone();
        test_item_detail.PROBECARD_LOADBOARD_ID = file_detail.PROBECARD_LOADBOARD_ID.clone();
        test_item_detail.START_TIME = file_detail.START_TIME;
        test_item_detail.END_TIME = file_detail.END_TIME;
        test_item_detail.START_HOUR_KEY = file_detail.START_HOUR_KEY.clone();
        test_item_detail.START_DAY_KEY = file_detail.START_DAY_KEY.clone();
        test_item_detail.END_HOUR_KEY = file_detail.END_HOUR_KEY.clone();
        test_item_detail.END_DAY_KEY = file_detail.END_DAY_KEY.clone();
        test_item_detail.FLOW_ID = file_detail.FLOW_ID.clone();
        test_item_detail.RETEST_BIN_NUM = file_detail.RETEST_BIN_NUM.clone();
        test_item_detail.PROCESS = file_detail.PROCESS.clone();
        test_item_detail.UPLOAD_TIME = file_detail.UPLOAD_TIME;
    }

    /// 构建SubFileDetail映射 (对应Scala中的FileDetailService.broadcastSubFileDetail)
    pub fn build_sub_file_detail_map(dws_die_detail: &[Arc<DwsSubDieDetail>],) -> std::collections::HashMap<i64, DwsSubFileDetail> {
        use std::collections::HashMap;

        let mut file_detail_map:HashMap<i64, DwsSubFileDetail>  = HashMap::new();

        // 按FILE_ID分组，每个FILE_ID取第一个记录来构建SubFileDetail
        for die in dws_die_detail {
            if let Some(file_id) = die.FILE_ID {
                if !file_detail_map.contains_key(&file_id) {
                    let sub_file_detail = DwsSubFileDetail {
                        CUSTOMER: die.CUSTOMER.clone(),
                        SUB_CUSTOMER: die.SUB_CUSTOMER.clone(),
                        UPLOAD_TYPE: die.UPLOAD_TYPE.clone(),
                        FILE_ID: die.FILE_ID,
                        FILE_NAME: die.FILE_NAME.clone(),
                        FILE_TYPE: die.FILE_TYPE.clone(),
                        FACTORY: die.FACTORY.clone(),
                        FACTORY_SITE: die.FACTORY_SITE.clone(),
                        FAB: die.FAB.clone(),
                        FAB_SITE: die.FAB_SITE.clone(),
                        LOT_TYPE: die.LOT_TYPE.clone(),
                        TEST_AREA: die.TEST_AREA.clone(),
                        OFFLINE_RETEST: die.OFFLINE_RETEST.map(|v| v as i32),
                        INTERRUPT: die.INTERRUPT.map(|v| v as i32),
                        DUP_RETEST: die.DUP_RETEST.map(|v| v as i32),
                        BATCH_NUM: die.BATCH_NUM.map(|v| v as i32),
                        LOT_ID: die.LOT_ID.clone(),
                        SBLOT_ID: die.SBLOT_ID.clone(),
                        PROBER_HANDLER_ID: die.PROBER_HANDLER_ID.clone(),
                        TESTER_TYPE: die.TESTER_TYPE.clone(),
                        TEST_STAGE: die.TEST_STAGE.clone(),
                        DEVICE_ID: die.DEVICE_ID.clone(),
                        TEST_PROGRAM: die.TEST_PROGRAM.clone(),
                        TEST_TEMPERATURE: die.TEST_TEMPERATURE.clone(),
                        TEST_PROGRAM_VERSION: die.TEST_PROGRAM_VERSION.clone(),
                        TESTER_NAME: die.TESTER_NAME.clone(),
                        PROBECARD_LOADBOARD_ID: die.PROBECARD_LOADBOARD_ID.clone(),
                        START_TIME: die.START_TIME,
                        END_TIME: die.END_TIME,
                        START_HOUR_KEY: die.START_HOUR_KEY.clone(),
                        START_DAY_KEY: die.START_DAY_KEY.clone(),
                        END_HOUR_KEY: die.END_HOUR_KEY.clone(),
                        END_DAY_KEY: die.END_DAY_KEY.clone(),
                        FLOW_ID: die.FLOW_ID.clone(),
                        RETEST_BIN_NUM: die.RETEST_BIN_NUM.clone(),
                        PROCESS: die.PROCESS.clone(),
                        UPLOAD_TIME: die.UPLOAD_TIME,
                    };
                    file_detail_map.insert(file_id, sub_file_detail);
                }
            }
        }

        file_detail_map
    }

    /// 将字符串集合去重并用逗号连接
    /// 对应Scala中的DwsCommonUtil.mkStringDistinct方法
    pub fn mk_string_distinct(strings: &[&String]) -> String {
        let mut unique_strings: Vec<String> = strings
            .iter()
            .filter(|s| !s.is_empty())
            .map(|s| s.to_string())
            .collect::<std::collections::HashSet<_>>()
            .into_iter()
            .collect();

        unique_strings.sort();
        unique_strings.join(",")
    }

    /// 判断是否为CP测试区域
    /// 参考Scala中的SUPPORT_CP_TEST_AREA_LIST.contains(TestArea.of(testArea))
    /// 对应Scala中的DwsService.isCpTestArea方法
    pub fn is_cp_test_area(test_area: &str) -> bool {
        use crate::model::constant::test_area::TestArea;

        // 获取CP测试区域列表
        let cp_test_area_list = TestArea::get_cp_list();

        // 将字符串转换为TestArea枚举
        if let Some(test_area_enum) = TestArea::of(test_area) {
            // 检查是否在CP测试区域列表中
            cp_test_area_list.contains(&test_area_enum)
        } else {
            // 如果无法识别的测试区域，默认返回false
            false
        }
    }
    

    /// 检查RETEST_BIN_NUM格式 (对应Scala中的checkRetestBinNum方法)
    pub fn check_retest_bin_num(retest_bin_num: &str) -> Arc<str> {
        // 正则表达式: \d+(,\d+)*  匹配数字和逗号分隔的数字
        let pattern = Regex::new(r"^\d+(,\d+)*$").unwrap();
        if pattern.is_match(retest_bin_num) {
            Arc::from(retest_bin_num)
        } else {
            Arc::from(ALL)
        }
    }

    /// 生成retestBinNumMaps (对应Scala中的broadcast retestBinNumMaps逻辑)
    pub fn generate_retest_bin_num_maps( dws_die_detail: &[Arc<DwsSubDieDetail>],) -> HashMap<Sblot, HashMap<String, i32>> {
        use std::collections::HashMap;
        
        // 过滤IS_DUP_FINAL_TEST == 1的记录
        let filtered_die_detail: Vec<_> = dws_die_detail
            .iter()
            .filter(|die| die.IS_DUP_FINAL_TEST == Some(1))
            .collect();
        
        // 按Sblot分组
        let mut sblot_groups: HashMap<Sblot, Vec<&Arc<DwsSubDieDetail>>> = HashMap::new();
        
        for die in filtered_die_detail {
            let sblot = Sblot {
                customer: die.CUSTOMER.to_string(),
                factory: die.FACTORY.to_string(),
                device_id: die.DEVICE_ID.to_string(),
                test_area: die.TEST_AREA.to_string(),
                lot_id: die.LOT_ID.to_string(),
                lot_type: die.LOT_TYPE.to_string(),
                sblot_id: die.SBLOT_ID.to_string(),
                test_stage: die.TEST_STAGE.to_string(),
                test_program: die.TEST_PROGRAM.to_string(),
            };
            
            sblot_groups.entry(sblot).or_insert_with(Vec::new).push(die);
        }
        
        // 对每个Sblot组，按RETEST_BIN_NUM分组并取OFFLINE_RETEST的最大值
        let mut retest_bin_num_maps = HashMap::new();
        
        for (sblot, records) in sblot_groups {
            let mut bin_map: HashMap<String, i32> = HashMap::new();
            
            // 按RETEST_BIN_NUM分组
            let mut bin_groups: HashMap<String, Vec<&Arc<DwsSubDieDetail>>> = HashMap::new();
            for record in records {
                let bin_num = record.RETEST_BIN_NUM.as_ref().to_string();
                bin_groups.entry(bin_num).or_insert_with(Vec::new).push(record);
            }
            
            // 对每个bin组取OFFLINE_RETEST的最大值
            for (bin_num, bin_records) in bin_groups {
                let max_offline_retest = bin_records
                    .iter()
                    .filter_map(|r| r.OFFLINE_RETEST)
                    .max()
                    .unwrap_or(0);
                bin_map.insert(bin_num, max_offline_retest);
            }
            
            retest_bin_num_maps.insert(sblot, bin_map);
        }
        
        retest_bin_num_maps
    }
}

/// DWS子Die详情
/// 对应Scala中的SubFileDetail类
#[derive(Debug, Clone)]
#[allow(non_snake_case)]
pub struct DwsSubFileDetail {
    pub CUSTOMER: Arc<str>,
    pub SUB_CUSTOMER: Arc<str>,
    pub UPLOAD_TYPE: Arc<str>,
    pub FILE_ID: Option<i64>,
    pub FILE_NAME: Arc<str>,
    pub FILE_TYPE: Arc<str>,
    pub FACTORY: Arc<str>,
    pub FACTORY_SITE: Arc<str>,
    pub FAB: Arc<str>,
    pub FAB_SITE: Arc<str>,
    pub LOT_TYPE: Arc<str>,
    pub TEST_AREA: Arc<str>,
    pub OFFLINE_RETEST: Option<i32>,
    pub INTERRUPT: Option<i32>,
    pub DUP_RETEST: Option<i32>,
    pub BATCH_NUM: Option<i32>,
    pub LOT_ID: Arc<str>,
    pub SBLOT_ID: Arc<str>,
    pub PROBER_HANDLER_ID: Arc<str>,
    pub TESTER_TYPE: Arc<str>,
    pub TEST_STAGE: Arc<str>,
    pub DEVICE_ID: Arc<str>,
    pub TEST_PROGRAM: Arc<str>,
    pub TEST_TEMPERATURE: Arc<str>,
    pub TEST_PROGRAM_VERSION: Arc<str>,
    pub TESTER_NAME: Arc<str>,
    pub PROBECARD_LOADBOARD_ID: Arc<str>,
    pub START_TIME: Option<i64>,
    pub END_TIME: Option<i64>,
    pub START_HOUR_KEY: Arc<str>,
    pub START_DAY_KEY: Arc<str>,
    pub END_HOUR_KEY: Arc<str>,
    pub END_DAY_KEY: Arc<str>,
    pub FLOW_ID: Arc<str>,
    pub RETEST_BIN_NUM: Arc<str>,
    pub PROCESS: Arc<str>,
    pub UPLOAD_TIME: Option<i64>,
}

/// 对应Scala中的SubDieDetail类
#[derive(Debug, Clone)]
#[allow(non_snake_case)]
pub struct DwsSubDieDetail {
    pub CUSTOMER: Arc<str>,
    pub SUB_CUSTOMER: Arc<str>,
    pub UPLOAD_TYPE: Arc<str>,
    pub FILE_ID: Option<i64>,
    pub FILE_NAME: Arc<str>,
    pub FILE_TYPE: Arc<str>,
    pub FACTORY: Arc<str>,
    pub FACTORY_SITE: Arc<str>,
    pub FAB: Arc<str>,
    pub FAB_SITE: Arc<str>,
    pub LOT_TYPE: Arc<str>,
    pub TEST_AREA: Arc<str>,
    pub ECID: Arc<str>,
    pub IS_STANDARD_ECID: Option<i32>,
    pub SITE: Option<i64>,
    pub OFFLINE_RETEST: Option<i32>,
    pub OFFLINE_RETEST_IGNORE_TP: Option<i32>,
    pub ONLINE_RETEST: Option<i32>,
    pub INTERRUPT: Option<i32>,
    pub INTERRUPT_IGNORE_TP: Option<i32>,
    pub DUP_RETEST: Option<i32>,
    pub DUP_RETEST_IGNORE_TP: Option<i32>,
    pub BATCH_NUM: Option<i32>,
    pub BATCH_NUM_IGNORE_TP: Option<i32>,
    pub IS_FIRST_TEST: Option<i32>,
    pub IS_FINAL_TEST: Option<i32>,
    pub IS_FIRST_TEST_IGNORE_TP: Option<i32>,
    pub IS_FINAL_TEST_IGNORE_TP: Option<i32>,
    pub IS_DUP_FIRST_TEST: Option<i32>,
    pub IS_DUP_FINAL_TEST: Option<i32>,
    pub IS_DUP_FIRST_TEST_IGNORE_TP: Option<i32>,
    pub IS_DUP_FINAL_TEST_IGNORE_TP: Option<i32>,
    pub WAFER_ID: Arc<str>,
    pub WAFER_NO: Arc<str>,
    pub LOT_ID: Arc<str>,
    pub SBLOT_ID: Arc<str>,
    pub WAFER_LOT_ID: Arc<str>,
    pub PROBER_HANDLER_ID: Arc<str>,
    pub TESTER_TYPE: Arc<str>,
    pub OPERATOR_NAME: Arc<str>,
    pub TEST_STAGE: Arc<str>,
    pub DEVICE_ID: Arc<str>,
    pub TEST_PROGRAM: Arc<str>,
    pub TEST_TEMPERATURE: Arc<str>,
    pub TEST_PROGRAM_VERSION: Arc<str>,
    pub TESTER_NAME: Arc<str>,
    pub PROBECARD_LOADBOARD_ID: Arc<str>,
    pub FLOW_ID: Arc<str>,
    pub FLOW_ID_IGNORE_TP: Arc<str>,
    pub DIE_CNT: Option<i64>,
    pub X_COORD: Option<i32>,
    pub Y_COORD: Option<i32>,
    pub DIE_X: Option<i32>,
    pub DIE_Y: Option<i32>,
    pub WF_FLAT: Arc<str>,
    pub FABWF_ID: Arc<str>,
    pub POS_X: Arc<str>,
    pub POS_Y: Arc<str>,
    pub RETICLE_X: Option<i32>,
    pub RETICLE_Y: Option<i32>,
    pub SITE_ID: Arc<str>,
    pub TEST_TIME: Option<i64>,
    pub PART_ID: Arc<str>,
    pub SBIN_NUM: Option<i64>,
    pub SBIN_PF: Arc<str>,
    pub SBIN_NAM: Arc<str>,
    pub HBIN_NUM: Option<i64>,
    pub HBIN_PF: Arc<str>,
    pub HBIN_NAM: Arc<str>,
    pub HBIN: Arc<str>,
    pub SBIN: Arc<str>,
    pub START_TIME: Option<i64>,
    pub END_TIME: Option<i64>,
    pub START_HOUR_KEY: Arc<str>,
    pub START_DAY_KEY: Arc<str>,
    pub END_HOUR_KEY: Arc<str>,
    pub END_DAY_KEY: Arc<str>,
    pub C_PART_ID: Option<i64>,
    pub RETEST_BIN_NUM: Arc<str>,
    pub PROCESS: Arc<str>,
    pub CREATE_TIME: Option<i64>,
    pub UPLOAD_TIME: Option<i64>,
    pub TEST_HEAD: Option<i64>,
    pub PROBER_HANDLER_TYP: Arc<str>,
    pub PROBECARD_LOADBOARD_TYP: Arc<str>,
}

/// DWS子测试项详情
/// 对应Scala中的SubTestItemDetail类
#[derive(Debug, Clone)]
#[allow(non_snake_case)]
pub struct DwsSubTestItemDetail {
    pub CUSTOMER: Arc<str>,
    pub SUB_CUSTOMER: Arc<str>,
    pub UPLOAD_TYPE: Arc<str>,
    pub FILE_ID: Option<i64>,
    pub FILE_NAME: Arc<str>,
    pub FILE_TYPE: Arc<str>,
    pub FACTORY: Arc<str>,
    pub FACTORY_SITE: Arc<str>,
    pub FAB: Arc<str>,
    pub FAB_SITE: Arc<str>,
    pub LOT_TYPE: Arc<str>,
    pub TEST_AREA: Arc<str>,
    pub ECID: Arc<str>,
    pub OFFLINE_RETEST: Option<i32>,
    pub ONLINE_RETEST: Option<i32>,
    pub INTERRUPT: Option<i32>,
    pub DUP_RETEST: Option<i32>,
    pub BATCH_NUM: Option<i32>,
    pub IS_FIRST_TEST: Option<i32>,
    pub IS_FINAL_TEST: Option<i32>,
    pub IS_FIRST_TEST_IGNORE_TP: Option<i32>,
    pub IS_FINAL_TEST_IGNORE_TP: Option<i32>,
    pub IS_DUP_FIRST_TEST: Option<i32>,
    pub IS_DUP_FINAL_TEST: Option<i32>,
    pub IS_DUP_FIRST_TEST_IGNORE_TP: Option<i32>,
    pub IS_DUP_FINAL_TEST_IGNORE_TP: Option<i32>,
    pub WAFER_ID: Arc<str>,
    pub WAFER_NO: Arc<str>,
    pub LOT_ID: Arc<str>,
    pub SBLOT_ID: Arc<str>,
    pub WAFER_LOT_ID: Arc<str>,
    pub PROBER_HANDLER_ID: Arc<str>,
    pub TESTER_TYPE: Arc<str>,
    pub TEST_STAGE: Arc<str>,
    pub DEVICE_ID: Arc<str>,
    pub TEST_PROGRAM: Arc<str>,
    pub TEST_TEMPERATURE: Arc<str>,
    pub TEST_PROGRAM_VERSION: Arc<str>,
    pub TESTER_NAME: Arc<str>,
    pub PROBECARD_LOADBOARD_ID: Arc<str>,
    pub SITE: Option<i64>,
    pub X_COORD: Option<i32>,
    pub Y_COORD: Option<i32>,
    pub PART_ID: Arc<str>,
    pub SBIN_NUM: Option<i64>,
    pub SBIN_PF: Arc<str>,
    pub SBIN_NAM: Arc<str>,
    pub HBIN_NUM: Option<i64>,
    pub HBIN_PF: Arc<str>,
    pub HBIN_NAM: Arc<str>,
    pub HBIN: Arc<str>,
    pub SBIN: Arc<str>,
    pub TEST_NUM: Option<i64>,
    pub TEST_TXT: Arc<str>,
    pub TEST_ITEM: Arc<str>,
    pub TEST_ORDER: Option<i64>,
    pub LO_SPEC: Option<f64>,
    pub HI_SPEC: Option<f64>,
    pub LO_LIMIT: Option<f64>,
    pub HI_LIMIT: Option<f64>,
    pub ORIGIN_HI_LIMIT: Option<f64>,
    pub ORIGIN_LO_LIMIT: Option<f64>,
    pub TEST_VALUE: Option<f64>,
    pub TEST_RESULT: Option<i32>,
    pub START_TIME: Option<i64>,
    pub END_TIME: Option<i64>,
    pub START_HOUR_KEY: Arc<str>,
    pub START_DAY_KEY: Arc<str>,
    pub END_HOUR_KEY: Arc<str>,
    pub END_DAY_KEY: Arc<str>,
    pub FLOW_ID: Arc<str>,
    pub UNITS: Arc<str>,
    pub ORIGIN_UNITS: Arc<str>,
    pub TESTITEM_TYPE: Arc<str>,
    pub C_PART_ID: Option<i64>,
    pub RETEST_BIN_NUM: Arc<str>,
    pub PROCESS: Arc<str>,
    pub CREATE_TIME: Option<i64>,
    pub UPLOAD_TIME: Option<i64>,
}
